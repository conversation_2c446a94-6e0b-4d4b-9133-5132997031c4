package com.atour.cms.audit.mapper.acms;

import com.atour.cms.audit.entity.acms.ProjectNodeEntity;
import com.mybatisflex.annotation.UseDataSource;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * ProjectNodeMapper
 *
 * <AUTHOR>
 */
@UseDataSource("con")
public interface ProjectNodeMapper extends BaseMapper<ProjectNodeEntity> {
    /**
     * 获取待验收的项目ID
     *
     * @param applyNodeCode         申请节点
     * @param checkNodeCode         验收节点
     * @param finishStatusCode      完成状态
     * @param unCommittedStatusCode 未提交状态
     * @return 结果
     */
    @Select("SELECT project_id " +
            "FROM t_project_group " +
            "WHERE (node_code = #{applyNodeCode} AND node_status = #{finishStatusCode}) " +
            "   OR (node_code = #{checkNodeCode} AND node_status = #{unCommittedStatusCode}) " +
            "GROUP BY project_id " +
            "HAVING COUNT(DISTINCT CASE WHEN node_code = #{applyNodeCode} AND node_status = #{finishStatusCode} THEN 1 END) > 0 " +
            "   AND COUNT(DISTINCT CASE WHEN node_code = #{checkNodeCode} AND node_status = #{unCommittedStatusCode} THEN 1 END) > 0")
    List<String> selectPreAuditTaskProjectIds(String applyNodeCode, String checkNodeCode, String finishStatusCode, String unCommittedStatusCode);

    /**
     * 更新节点值
     *
     * @param tableName 表名
     * @param nodeCode  节点编码
     * @param nodeValue 节点值
     * @param projectId 项目ID
     * @return 结果
     */
    @Update("""
            UPDATE ${tableName} SET `remark` = #{nodeValue}  WHERE `project_id` = #{projectId} AND `node_code` = #{nodeCode}
            """)
    int updateNodeValue(String tableName, String nodeCode, String nodeValue, String projectId);
}
