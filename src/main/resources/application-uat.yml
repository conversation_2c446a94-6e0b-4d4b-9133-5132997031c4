#端口
server:
  port: 20001

spring:
  application:
    name: audit-service
  #jackson设置
  jackson:
    default-property-inclusion: non_null
  # 大小配置
  codec:
    max-in-memory-size: 1MB

#Nacos配置
nacos:
  enabled: false
  server-addr: **************:8848
  namespace: atour
  group: dev

#Eureka配置
eureka:
  client:
    enabled: true
    service-url:
      defaultZone: http://qa-eureka.at-our.com:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

#redis配置
redis:
  address:
    - redis://qa-redis.at-our.com:6379
  password: Atour2017
  database: 67

#mybatis-flex配置
mybatis-flex:
  datasource:
    audit:
      url: ************************************************************************************************************************************************************************************************************
      username: yaduodb
      password: $^mvObKz6mIfnsWbBd6vYVjl
    con:
      url: **************************************************************************************************************************************************
      username: root
      password: Root_12root


#OSS配置
oss:
  #存储类型, Local和OSS
  storage:
    type: OSS
    path: '/data/upload'
    preview: '/data/preview'
    bucket-name: acms-file
    dir-name: audit-tools
    endpoint: oss-cn-qingdao.aliyuncs.com
    region: cn-qingdao
    access-key-id: LTAI5tKZ2p5ag1MoboinRCtk
    access-key-secret: ******************************
    #过期时间，单位：分钟
    presigned-url-expiration: 60

jwt:
  secret: KC3ArCVG9A1/U1j1fFzQGIrIn8CxQw2Pr+WGqIL+ydg=
  expire: 1

auth:
  api:
    excludes:
      - /audit-service/api/v1/sso/login
      - /audit-service/api/v1/sso/feishu/login
      - /audit-service/api/v1/sso/feishu/appId
      - /audit-service/api/v1/sse/progress
      - /audit-service/api/v1/sso/rsa/key

#飞书配置
feishu:
  app:
    id: cli_a8c251e1aaec900b
    secret: bEcYVphUbySxAXGIXRYhQePVnNzvWcnQ
  api:
    tenant-access-token: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
    user-access-token: https://open.feishu.cn/open-apis/authen/v2/oauth/token
    user-info: https://open.feishu.cn/open-apis/authen/v1/user_info
    jsapi-ticket: https://open.feishu.cn/open-apis/jssdk/ticket/get
    message: https://open.feishu.cn/open-apis/im/v1/messages
    user-id: https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id

# Xxl-job配置
xxl:
  job:
    admin:
      addresses: https://qa-xxljob.at-our.com/xxl-job-admin
    accessToken: CHpBiIvZvwXmz4Sk
    executor:
      appname: atour-audit-service
      address: ""
      ip: ""
      port: 40001
      logpath: logs/audit-service
      logretentiondays: 30

# RSA配置
rsa:
  private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDAQ/2lCsmPz6BugLdLY0cAkaMNiNSzfpUsRy2mSKj5gNzWU7CTmOisQRhOCp4I2lXoUT7PLXDQtH/Qu17YHgdTZOVSIBYpFcDm9Ya5KdDqw7e2odWf2Mq18C4qHJZmQmUQ1xsGoTYfDigdfyV9e8W/JirLalEhNt6xCXw1iMi0uhYyqRkdF4qdacbaYZtrNTc9y9ciUn+3lVtG7PxwIPCdBIQcFBqwmXIx7gmja5B5TaQPC32Be9o80iLP7WGJCFXh3PJNFYBf6JYxT3m0S0qXdkwmhVuJar2hAfI1xTjeJPg7lkUn1yp+foaxgaxdTreIwyTE1V3QBkALEh9OSxDJAgMBAAECggEAEKTaJHNVfVn4nhRNucydeQjChCGOIz9AJ+n+A0z7NsUvaOzsBmz4aHAGq/vakDLheLfuz2XURDb58R6FUIFx+ifL+9LsBDC+RygmWmlqrOYWRWzHVtwAOWWSQ4I/yViyUdXDQMzMJ/5UXfBTtSQVu21RC1S38v+CYfJLosFFebKTIMD82zLwXgdWWFyVVNkA6jOdcZi5bhMjx83lnhlJ4HHe5jPcelqWrTlaU8A9wOnBbrt9+PDggVrBpui+HAAhtS4RWRN14QXksAmR1VQQGKKMWPhiJ4tpJX0oeeBjiQiVC4t42hwqAHxM2to+jbtpQ7vXPFWqKzv71B2K6fAbwQKBgQD4bfjZiIFiGLgrd6epuE90MRBFQGCUG5euuVReJMhbYABLR86f1KwbV2HBHKdD/GvkNxfHhL/HxGyXHfataRN/v87VQNcUm3VM9ttosLnrX39wSRcMqT8BGlM4Wn23klq+B7nJiOh3WwFro32I/A4IOinSOILdaZ9w6CNOWBtvQQKBgQDGH+B/MgwLfpCSQZS/lk7nWP1DMJHqCgJRJFgiqEf8AzJyQZk0B1xVwEG/vhQ0s3UvRRRxrfqqd9fndOq9Uad5kOHAJ9EoU3pmfPVzjOStmvgSg4ea8ICgeECA1KtTrh561euAkzSRWJLYY8vXE++X2nnCoDndPVafwLYhDOHHiQKBgQCM7gXvkaqkNZW0wupQM4iPR44NWxnuq0B/FA+yYHdwrqM8EX472W/RCt8fp9uTi8zA6CgLPVl2QA3CJHNdqHAxVmRFQcVBoXcHtDp/x1GRHtmynyP+QLzHKWHwZBVRYCsuBj/0/j+yBeRmZZa/cQ2cyTIUWY6TgGm8qVPuC7l0gQKBgF3aGdmHFqeXQ9rUkCcVh7kVsZtTdbt9FE3lvo5kUv0xcoMnSfCYkAfa9Aple2Lfo+JJgf674KNB9BejjdMyy/fNRvNkqTLGIJROG2OqWWVXpK34epNN3FCnxj6A/8u7Dps3nMCieuNjJuS0mYn/2p5/kvA8QEzqUo+IlX4ggQ+pAoGAdYBLNRyLQG7w2HcgoLIzRKBamfiGC0iZMqc2/LCTtOoBDwTVAef7UdH9AeO4qubwx5MokI6gm3K0Xsw4sfZKQ0KZoxjhqmsiq1TjA+kW7w0ghbxOWwmP83D0H8tHoj5+gV0fGtWgYtU5cKduVqkfJ/bpnHlqwUSvr0ojbItl1rQ=
  public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwEP9pQrJj8+gboC3S2NHAJGjDYjUs36VLEctpkio+YDc1lOwk5jorEEYTgqeCNpV6FE+zy1w0LR/0Lte2B4HU2TlUiAWKRXA5vWGuSnQ6sO3tqHVn9jKtfAuKhyWZkJlENcbBqE2Hw4oHX8lfXvFvyYqy2pRITbesQl8NYjItLoWMqkZHReKnWnG2mGbazU3PcvXIlJ/t5VbRuz8cCDwnQSEHBQasJlyMe4Jo2uQeU2kDwt9gXvaPNIiz+1hiQhV4dzyTRWAX+iWMU95tEtKl3ZMJoVbiWq9oQHyNcU43iT4O5ZFJ9cqfn6GsYGsXU63iMMkxNVd0AZACxIfTksQyQIDAQAB
  # ACMS系统的私钥
  acms:
    private-key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

#房间检查规则
room:
  check:
    # 最少房间数
    min: 15
    # 最少比例
    ratio: 0.15

# website
website:
  url:
    prefix: http://218.107.242.90:9962