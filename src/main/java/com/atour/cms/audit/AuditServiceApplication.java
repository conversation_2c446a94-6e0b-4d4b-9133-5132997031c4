package com.atour.cms.audit;

import com.atour.cms.audit.config.nacos.NacosConfigInitializer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

@SpringBootApplication
@MapperScan("com.atour.cms.audit.mapper")
@EnableEurekaClient
public class AuditServiceApplication {

    public static void main(String[] args) {
        com.spire.presentation.license.LicenseProvider.setLicenseKey("B0pK1P1D2Z8BAB2FJmmmdiYHl9h7Y99BWjx3XVBNqAbN5zjUq20k/CkXn8F4LaQjENFs1OZV+NPcteohWyy3PQ23MFAl9hP1yaokolaGw83IiEsiDdZRjJQWvYjCQDn6tn1pxUK0GU2prNbx71Qhn2aHI1E7lVGNCJ1C019HBIjf28DTCPhGaqdONQbImnBARCMw2d+G9TZBg9GJiyYkycKFZZQkwnT/Hqu2ckstrKMuoryhRXrlalKEJjwMDvthrR+g/g7d4rAxsrBeqPf3vAllBGsaVM7HCAHAdZ5skF3QIW/H9iiQBwbNiVyYKZCLYAIMQI335rlak6llvK8IQvSC3fOsmrrlU+kYmGLI0AhlpvZDfYpxPC9jHvZLsgOWRDw8vILdSIaJITCxtBf2wnlzoFQtawzxXEMV67RjITKtGLmeDTXRyvB4YpilkOz9RWj9S0TT9zncaPURz7tRzznHAJJKqPnq1RiyZL9UaxUH0KfP0slnxx2bL4qU4yhu1u1j7qcLTaCTXHnc5zRnLzSjcDaUUdZsvDd5ZvYtXsfxPrk1/eJhr+iN03GkIf6q8YaA5/emQoSI9uHlOYWRoacYQW6iFY/m/0ozZkGRvC+RbrQXEiWbrDRjp2h+sD6f5+/Vs7MGmdK/PYwf3yjc3258kV3oZDwDgtyKj3Gtq1taxA0oBHwTQLBgG+aMp/vCgIf1mq65jGOxb8HvM4C/LgSEjK8fY00K/aDAw6JU4MIuHEa9iOSXVmZHifZGdmHkHBvPNjsfVwt3TGMw64lxX4txzWwXC4X14nb3kZCniYkjqoEYZJ1t5wwcrAROsFmYnmDRuLCy8NZ/FGIxju0Bo8tIT7eAFK2mmWrZYJw3IERP4ER6Pjg5gtthVXtid94g9go1SD10q+Txj7scZCZxo1AdS0vSGjhHMNo3e5QysUCX5L1TugM/+Vy1cK+0buPcPG7XWRnBt3GxRIhvJafowE3MwNNOCKqLmoChThh2sAkxliqIW6BKY51B9wwQoiDinkaHtTkhViHQaL9xdgQ7rgyyzGO6Y34DX6EwEw4HGWYlIM0Zdv2sbUpqBzt+kt/RMbRuRmRw1PyzCMgFs/ulqCLQjdR7OaFVoR1vJWJfgXnmYZtiN3s7UlhdYLTf5eM/xB+BZCxqdV1BbYCEx1Th1q3y/gAVx7aPptGTKFSmT8uX7sHU1N60S0g+1lvw14iIqLtKKtRkexzltrm9XmgF/4jFjeUgfScL7ZWlgPa8NmmmznMZQ2oHt3QWivNw6sHTYWnEpVyZwJEeFkSTxlhYWDu1IlruhAeE/9Rbvhpas5Hyn7jOiUTlNSK9+VfCb6sJck5ASNpsXcXAN/uYHFmCtgn+GjTbH79MbFOn4rj+EHd55jC7H4kQb6pfCIUH4Mp6P2v7X2DeuDGRaL8TSZfQQ1NJU+NZMcksJ11h87Tw9+9RO41+xDHG4hj/6D5xv3gB4f0RVDaRWkLs4WcIeHdI8nRTSSITktTlEVjItBhYFshU9yrigoNYu+EJgkTRpXdfsUYaxUKa+JVvHeL245hAYKUPCpMVp3CbSz7wOUwSh7rsvbQZGdqoJwFL/iD4p61t8V7bFa8jqLsblf4N6KeQcte69zUBKr61DslxLz8E/fwNtzYDCKd3hzcUENEpLxQb8sfcO2Y1K5c=");
        SpringApplicationBuilder builder = new SpringApplicationBuilder(AuditServiceApplication.class);
        // nacos配置
        builder.initializers(new NacosConfigInitializer());
        builder.run(args);
    }

}
