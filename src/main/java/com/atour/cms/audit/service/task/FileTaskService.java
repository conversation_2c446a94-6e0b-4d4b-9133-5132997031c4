package com.atour.cms.audit.service.task;

import com.atour.cms.audit.constant.EnumStorageType;
import com.atour.cms.audit.constant.acms.EnumProjectNode;
import com.atour.cms.audit.constant.system.EnumRole;
import com.atour.cms.audit.constant.task.EnumFileTaskStatus;
import com.atour.cms.audit.dto.PageInfo;
import com.atour.cms.audit.dto.PageQuery;
import com.atour.cms.audit.dto.Response;
import com.atour.cms.audit.dto.task.FileTaskQueryDto;
import com.atour.cms.audit.dto.task.TaskCheckResultAndReformDto;
import com.atour.cms.audit.dto.task.TaskFinishAcceptanceSummaryDto;
import com.atour.cms.audit.dto.task.TaskHistoryDto;
import com.atour.cms.audit.entity.acms.ProjectInfoEntity;
import com.atour.cms.audit.entity.acms.ProjectNodeEntity;
import com.atour.cms.audit.entity.acms.SupplierInfoEntity;
import com.atour.cms.audit.entity.basedata.ProjectMemberEntity;
import com.atour.cms.audit.entity.system.OssEntity;
import com.atour.cms.audit.entity.task.FileTaskEntity;
import com.atour.cms.audit.entity.task.TaskHistoryEntity;
import com.atour.cms.audit.entity.task.TaskListEntity;
import com.atour.cms.audit.exception.CommonException;
import com.atour.cms.audit.mapper.basedata.OssMapper;
import com.atour.cms.audit.mapper.task.FileTaskMapper;
import com.atour.cms.audit.mapper.task.TaskHistoryMapper;
import com.atour.cms.audit.service.acms.AcmsProjectService;
import com.atour.cms.audit.service.basedata.ProjectService;
import com.atour.cms.audit.service.system.OssService;
import com.atour.cms.audit.util.*;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.spire.presentation.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.awt.geom.Rectangle2D;
import java.io.*;
import java.net.URL;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class FileTaskService {
    @Value("${oss.storage.type}")
    private String storageType;
    @Value("${oss.storage.path}")
    private String storagePath;
    private static final String EXPORT_PPT_TASK_PREFIX = "export-ppt-task";

    @Resource
    private FileTaskMapper fileTaskMapper;
    @Resource
    private OssMapper ossMapper;
    @Resource
    private OssService ossService;
    @Resource
    private AcmsProjectService acmsProjectService;
    @Resource
    private ProjectService projectService;
    @Resource
    private TaskHistoryMapper taskHistoryMapper;
    @Resource
    private TaskExecutor taskExecutor;

    public Mono<Response<PageInfo<FileTaskEntity>>> getFileTaskPage(PageQuery<FileTaskQueryDto> pageQuery) {
        return createQueryWrapper(pageQuery.getQueryModel()).flatMap(queryWrapper -> {
            Page<FileTaskEntity> page = fileTaskMapper.paginateAs(pageQuery.getCurrent(), pageQuery.getPageSize(), queryWrapper, FileTaskEntity.class);
            return RestUtils.success(RestUtils.buildPage(page));
        });
    }

    public Mono<Response<String>> addExportPptFileTask(List<String> taskIds) {
        return UserUtils.get().flatMap(currentUser -> {
            List<TaskHistoryDto> historyIdAndProjectName = getHistoryIdAndProjectName(taskIds);
            if (CollectionUtils.isEmpty(historyIdAndProjectName)) {
                return RestUtils.fail("未查询到待导出的任务信息");
            }

            List<FileTaskEntity> fileTaskEntityList = new ArrayList<>();
            for (TaskHistoryDto taskHistoryDto : historyIdAndProjectName) {
                FileTaskEntity entity = new FileTaskEntity();
                entity.buildCreateInfo(currentUser);
                entity.setId(IdUtils.nextSnowFlakeId());
                entity.setProcess(0);
                entity.setState(EnumFileTaskStatus.NOTBEGINING.getIndex());
                // 组装导出ppt的文件名称
                String fileName = "ID" + taskHistoryDto.getProjectNo() + taskHistoryDto.getProjectName() + taskHistoryDto.getBrandName() + "_竣工验收报告";
                String fileType = ".pptx";
                entity.setOriginalName(fileName + fileType);
                entity.setFileName(IdUtils.uuid() + fileType);
                entity.setFileType(fileType);
                entity.setBusinessId(taskHistoryDto.getProjectId());
                entity.setBusinessName(taskHistoryDto.getProjectName());
                entity.setSecBusinessId(taskHistoryDto.getHistoryId());

                fileTaskEntityList.add(entity);
            }

            if (!fileTaskEntityList.isEmpty()) {
                fileTaskMapper.insertBatch(fileTaskEntityList);
            }

            // 异步执行 PPT 导出逻辑
            /*Mono.fromRunnable(() -> {
                try {
                    exportPptAsync(entity, taskHistoryDto, checkResultAndReforms, taskFinishAcceptanceSummary);
                } catch (Exception e) {
                    log.info("PPT 导出失败: " + e.getMessage());
                    entity.setErrorMessage(e.getMessage());
                    updateTaskStatus(entity, EnumFileTaskStatus.FAILED.getIndex(), false);
                }
            }).subscribeOn(Schedulers.boundedElastic())
                    .subscribe();*/
            return RestUtils.success("请到文件中心查看导出进度");
        });
    }

    public String beginExportPptFileTask() {
        QueryWrapper queryWrapper = QueryWrapper.create()
              .eq(FileTaskEntity::getState, EnumFileTaskStatus.NOTBEGINING.getIndex())
                .orderBy(FileTaskEntity::getCreateTime, true);
        List<FileTaskEntity> fileTaskEntities = fileTaskMapper.selectListByQuery(queryWrapper);
        if (CollectionUtils.isEmpty(fileTaskEntities)) {
            log.info("未查询到待导出的任务信息");
            return "未查询到待导出的任务信息";
        }

        fileTaskEntities.forEach(entity -> {
            // 防止重复导出，使用缓存控制
            String taskId = CacheUtils.getRedis(EXPORT_PPT_TASK_PREFIX, entity.getId());
            if (!StringUtils.hasText(taskId)) {
                CacheUtils.setRedis(EXPORT_PPT_TASK_PREFIX, entity.getId(), entity.getId(), null);
                taskExecutor.execute(() -> {
                    try {
                        List<TaskCheckResultAndReformDto> checkResultAndReforms = fileTaskMapper.getTaskCheckResultAndReformDtoList(entity.getSecBusinessId());
                        List<TaskFinishAcceptanceSummaryDto> taskFinishAcceptanceSummary = getTaskFinishAcceptanceSummary(entity.getSecBusinessId());
                        if (checkResultAndReforms.isEmpty() && taskFinishAcceptanceSummary.isEmpty()) {
                            log.info("未查询到待导出的任务信息");
                        }
                        TaskHistoryEntity taskHistoryEntity = taskHistoryMapper.selectOneById(entity.getSecBusinessId());
                        TaskHistoryDto taskHistoryDto = new TaskHistoryDto();
                        taskHistoryDto.setProjectId(entity.getBusinessId());
                        taskHistoryDto.setProjectName(entity.getBusinessName());
                        taskHistoryDto.setCheckTime(taskHistoryEntity.getCheckTime());
                        // 更新任务状态为处理中
                        updateTaskStatus(entity, EnumFileTaskStatus.PROCESSING.getIndex(), false);
                        // 导出ppt逻辑
                        exportPptAsync(entity, taskHistoryDto, checkResultAndReforms, taskFinishAcceptanceSummary);
                    } catch (Exception e) {
                        log.info("PPT 导出失败: " + e.getMessage());
                        entity.setErrorMessage(e.getMessage());
                        updateTaskStatus(entity, EnumFileTaskStatus.FAILED.getIndex(), false);
                    } finally {
                        // 清除缓存,5秒后清理
                        CacheUtils.setRedis(EXPORT_PPT_TASK_PREFIX, entity.getId(), entity.getId(), Duration.ofSeconds(5));
                    }
                });
            }
        });

        return "success";
    }

    private List<TaskHistoryDto> getHistoryIdAndProjectName(List<String> taskIds) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("a.id as historyId, a.task_id as taskId, a.check_time as checkTime, b.project_no as projectNo, b.project_id as projectId, b.project_name as projectName, b.brand_name as brandName")
                .from(TaskHistoryEntity.class).as("a")
                .innerJoin(TaskListEntity.class).as("b")
                .on("a.task_id = b.id AND a.check_num = b.check_times")
                .where(QueryMethods.column("a.task_id").in(taskIds));
        return fileTaskMapper.selectListByQueryAs(queryWrapper, TaskHistoryDto.class);
    }

    private List<TaskFinishAcceptanceSummaryDto> getTaskFinishAcceptanceSummary(String historyId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("ckresult.task_id as taskId, ckresult.history_id as historyId, ckresult.category_id as categoryId, ckresult.category_name as categoryName, " +
                        "ckresult.first_category_id as firstCategoryId, ckresult.first_category_name as firstCategoryName, ckresult.second_category_id as secondCategoryId, " +
                        "ckresult.second_category_name as secondCategoryName, ckresult.question_placeholder as checkQuestionPlacehoder, ckresult.question_file_id as checkQuestionFileId, " +
                        "ckresult.file_id as checkFileId, ckresult.comment as checkComment, rmresult.floor_num as floorNum, rmresult.room_num as roomNum, " +
                        "rmresult.question_placeholder as rmCheckQuestionPlacehoder, rmresult.question_file_id as rmCheckQuestionFileId, rmresult.file_id as rmCheckFileId, " +
                        "rmresult.comment as rmCheckComment")
                .from("t_audit_task_check_result").as("ckresult")
                .leftJoin("t_audit_task_check_result_room").as("rmresult")
                .on("ckresult.history_id = rmresult.history_id AND ckresult.id = rmresult.check_id")
                .where("ckresult.history_id = ? AND ckresult.question_type = 6", historyId)
                .orderBy("rmresult.floor_num, rmresult.room_num");

        List<TaskFinishAcceptanceSummaryDto> lists = fileTaskMapper.selectListByQueryAs(queryWrapper, TaskFinishAcceptanceSummaryDto.class);
        return lists;
    }

    /**
     * 异步导出 PPT 的方法
     *
     * @param entity 文件任务实体
     * @param taskFinishAcceptanceSummary
     */
    private void exportPptAsync(FileTaskEntity entity, TaskHistoryDto taskHistoryDto, List<TaskCheckResultAndReformDto> checkResultAndReforms, List<TaskFinishAcceptanceSummaryDto> taskFinishAcceptanceSummary) {
        try {
            // 生成 PPT 内容
            log.info("开始执行填充 PPT 文件内容...");
            byte[] pptBytes = generatePptContent(taskHistoryDto, checkResultAndReforms, taskFinishAcceptanceSummary);
            log.info("结束执行填充 PPT 文件内容...");
            // 保存到本地或上传到 OSS
            log.info("开始执行保存 PPT 文件到本地或上传到 OSS...");
            saveToOss(pptBytes, entity);
            // 更新任务状态
            Long fileSize = (long) pptBytes.length;
            entity.setFileSize(fileSize);
            log.info("完成导出，更新导出 PPT 任务状态...");
            updateTaskStatus(entity, EnumFileTaskStatus.COMPLETED.getIndex(), true);
        } catch (Exception e) {
            throw new CommonException("exportPptAsync方法报错:" + e.getMessage());
        }
    }

    /**
     * 生成 PPT 内容
     */
    private byte[] generatePptContent(TaskHistoryDto taskHistoryDto, List<TaskCheckResultAndReformDto> checkResultAndReforms, List<TaskFinishAcceptanceSummaryDto> taskFinishAcceptanceSummary) throws Exception {
        Presentation sourcePpt= new Presentation();
        sourcePpt.loadFromFile("template/template.pptx");

        Presentation targetPpt = new Presentation();
        targetPpt.getSlides().removeAt(0);
        // 模板首页
        ISlide slideOne = sourcePpt.getSlides().get(0);
        targetPpt.getSlides().insert(0, slideOne);
        Map<String, String> textMap = new HashMap<>();
        textMap.put("#projectName#", StringUtils.hasText(taskHistoryDto.getProjectName()) ? taskHistoryDto.getProjectName() : "无项目名称");
        replaceText(targetPpt.getSlides().get(targetPpt.getSlides().getCount() - 1), textMap);
        // 模板第二页，填充列表 todo 待亚朵提供数据
        ISlide slideTwo = sourcePpt.getSlides().get(1);
        targetPpt.getSlides().append(slideTwo);
        preSetDefaultValue(textMap);
        ProjectInfoEntity projectInfo = acmsProjectService.getProjectInfo(taskHistoryDto.getProjectId());
        if (Objects.isNull(projectInfo)) {
            log.info("不存在projectId为: {} 的项目信息", taskHistoryDto.getProjectId());
        }
//        System.out.println(">>>>>>>>>" + JsonUtils.toJsonString(projectInfo));
        if (Objects.nonNull(projectInfo) && StringUtils.hasText(projectInfo.getNodeTableName())) {
            List<ProjectNodeEntity> projectNodeList = acmsProjectService.getProjectNodeList(taskHistoryDto.getProjectId(), projectInfo.getNodeTableName(), Arrays.asList(EnumProjectNode.DESIGN_ROOM_NUM, EnumProjectNode.CHECK_ROOM_NUM));
//            System.out.println(">>>>>>>>>" + JsonUtils.toJsonString(projectNodeList));
            // 获取房间数量
            for (ProjectNodeEntity projectNode : projectNodeList) {
                if (EnumProjectNode.DESIGN_ROOM_NUM.getCode().equals(projectNode.getNodeCode())) {
                    textMap.put("#roomNum#", StringUtils.hasText(projectNode.getNodeValue()) ? projectNode.getNodeValue() : "");
                }
                if (EnumProjectNode.CHECK_ROOM_NUM.getCode().equals(projectNode.getNodeCode())) {
                    textMap.put("#acceptNum#", StringUtils.hasText(projectNode.getNodeValue())? projectNode.getNodeValue() : "");
                }
            }
            // 获取总包厂商
            SupplierInfoEntity projectSupplier = acmsProjectService.getProjectSupplier(taskHistoryDto.getProjectId(), projectInfo.getNodeTableName(), EnumProjectNode.CONSTRUCTION_UNIT);
//            System.out.println(">>>>>>>>>" + JsonUtils.toJsonString(projectSupplier));
            if (Objects.nonNull(projectSupplier)) {
                textMap.put("#contractor#", StringUtils.hasText(projectSupplier.getSupNameCn())? projectSupplier.getSupNameCn() : "");
            }
        }
        // 获取项目经理、现长、验收人员等信息
        List<ProjectMemberEntity> projectMemberList = projectService.getProjectMemberList(taskHistoryDto.getProjectId(), Arrays.asList(EnumRole.DEVELOPMENT_MANAGER, EnumRole.OPERATION_LEADER, EnumRole.DELIVERY_ACCEPTANCE));
        if (Objects.nonNull(projectMemberList) && !projectMemberList.isEmpty()) {
//            System.out.println(">>>>>>>>>" + JsonUtils.toJsonString(projectMemberList));
            for (ProjectMemberEntity projectMember : projectMemberList) {
                if (EnumRole.DEVELOPMENT_MANAGER.getCode().equals(projectMember.getRoleCode())) {
                    textMap.put("#projectMgr#", StringUtils.hasText(projectMember.getUserNickName())? projectMember.getUserNickName() : "");
                }
                if (EnumRole.OPERATION_LEADER.getCode().equals(projectMember.getRoleCode())) {
                    textMap.put("#storeMgr#", StringUtils.hasText(projectMember.getUserNickName())? projectMember.getUserNickName() : "");
                }
                if (EnumRole.DELIVERY_ACCEPTANCE.getCode().equals(projectMember.getRoleCode())) {
                    textMap.put("#acceptor#", StringUtils.hasText(projectMember.getUserNickName())? projectMember.getUserNickName() : "");
                }
            }
        }
        // 设置验收日期
        if (Objects.nonNull(taskHistoryDto.getCheckTime())) {
            textMap.put("#acceptDate#", DateUtils.format(taskHistoryDto.getCheckTime(), "yyyy-MM-dd"));
        }
        /*textMap.put("#roomNum#", "103");
        textMap.put("#acceptNum#", "103");
        textMap.put("#contractor#", "江西边城建设有限公司");
        textMap.put("#designUnit#", "天元上筑");
        textMap.put("#projectMgr#", "君子羊");
        textMap.put("#storeMgr#", "梧桐");
        textMap.put("#acceptor#", "享耳");
        textMap.put("#acceptDate#", "2024-8-5");*/
        replaceText(targetPpt.getSlides().get(targetPpt.getSlides().getCount() - 1), textMap);
        // 模板第三页 - 总目录页
        ISlide slideThree = sourcePpt.getSlides().get(2);
        targetPpt.getSlides().append(slideThree);
        // 模板第四页 - 子目录页
        ISlide slideFour = sourcePpt.getSlides().get(3);
        targetPpt.getSlides().append(slideFour);
        // 模板第五页 - 竣工问题汇总
        if (!checkResultAndReforms.isEmpty()) {
            generateQuestionSummaryPpt(checkResultAndReforms, sourcePpt, targetPpt, textMap);
        }
        // 模板第6页 - 子目录页
        ISlide slideTemplate6 = sourcePpt.getSlides().get(5);
        targetPpt.getSlides().append(slideTemplate6);
        // 模板7页 - 酒店/机房概况
        if (!taskFinishAcceptanceSummary.isEmpty()) {
            generateHotelAndMachineSummaryPpt(taskFinishAcceptanceSummary, sourcePpt, targetPpt, textMap);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        targetPpt.saveToFile(outputStream, FileFormat.PPTX_2013);
        return outputStream.toByteArray();
    }

    private void preSetDefaultValue(Map<String, String> textMap) {
        textMap.put("#roomNum#", "");
        textMap.put("#acceptNum#", "");
        textMap.put("#contractor#", "");
        textMap.put("#designUnit#", "");
        textMap.put("#projectMgr#", "");
        textMap.put("#storeMgr#", "");
        textMap.put("#acceptor#", "");
        textMap.put("#acceptDate#", "");
    }

    private void generateHotelAndMachineSummaryPpt(List<TaskFinishAcceptanceSummaryDto> checkResultAndReforms, Presentation sourcePpt, Presentation targetPpt, Map<String, String> textMap) {
        log.info("开始生成酒店/机房概况...");
        // 处理非房间的记录，并生成ppt页面
        checkResultAndReforms.stream().filter(item -> !StringUtils.hasText(item.getRoomNum())).forEach(result -> {
            // 示例照片
            List<OssEntity> checkFileList = StringUtils.hasText(result.getCheckQuestionFileId()) ? ossService.list(result.getCheckQuestionFileId()) : new ArrayList<>();
            // 完工照片
            List<OssEntity> finishFileList = StringUtils.hasText(result.getCheckFileId()) ? ossService.list(result.getCheckFileId()) : new ArrayList<>();
            int maxSize = Math.max(checkFileList.size(), finishFileList.size());
            if (maxSize > 0) {
                for (int i = 0; i < maxSize; i++) {
                    try {
                        ISlide slideTemplate7 = sourcePpt.getSlides().get(6);
                        targetPpt.getSlides().append(slideTemplate7);
                        String subTitle = StringUtils.hasText(result.getFirstCategoryName()) ? result.getFirstCategoryName() : "";
                        if (StringUtils.hasText(result.getSecondCategoryName())) {
                            subTitle += "-" + result.getSecondCategoryName();
                        }
                        if (StringUtils.hasText(result.getCategoryName())) {
                            subTitle += "-" + result.getCategoryName();
                        }
                        textMap.put("#subTitle#", subTitle);
                        textMap.put("#instruction#", StringUtils.hasText(result.getCheckQuestionPlacehoder())? result.getCheckQuestionPlacehoder() : "");
                        textMap.put("#acceptanceRemark#", StringUtils.hasText(result.getCheckComment())? result.getCheckComment() : "");
                        ISlide targetSlide = targetPpt.getSlides().get(targetPpt.getSlides().getCount() - 1);
                        replaceText(targetSlide, textMap);

                        if (!CollectionUtils.isEmpty(checkFileList)) {
                            // 插入示例图片
                            if (i < checkFileList.size()) {
                                Rectangle2D.Double left = new Rectangle2D.Double(53, 95, 406, 266);
                                OssEntity checkPic = checkFileList.get(i);
                                drawVideoOrPic(targetSlide, checkPic, left);
                            } else {
                                Rectangle2D.Double left = new Rectangle2D.Double(53, 95, 406, 266);
                                OssEntity checkPic = checkFileList.get(checkFileList.size() - 1);
                                drawVideoOrPic(targetSlide, checkPic, left);
                            }
                        }
                        if (!CollectionUtils.isEmpty(finishFileList)) {
                            // 插入完工图片
                            if (i < finishFileList.size()) {
                                Rectangle2D.Double right = new Rectangle2D.Double(502, 95, 406, 266);
                                OssEntity finishPic = finishFileList.get(i);
                                drawVideoOrPic(targetSlide, finishPic, right);
                            } else {
                                Rectangle2D.Double right = new Rectangle2D.Double(502, 95, 406, 266);
                                OssEntity finishPic = finishFileList.get(finishFileList.size() - 1);
                                drawVideoOrPic(targetSlide, finishPic, right);
                            }
                        }
                    } catch (Exception e) {
                        throw new CommonException(e.getMessage());
                    }
                }
            }
        });
        // 处理房间的记录，并生成ppt页面
        checkResultAndReforms.stream().filter(item -> StringUtils.hasText(item.getRoomNum())).forEach(result -> {
            // 示例照片
            List<OssEntity> checkFileList = StringUtils.hasText(result.getRmCheckQuestionFileId()) ? ossService.list(result.getRmCheckQuestionFileId()) : new ArrayList<>();
            // 完工照片
            List<OssEntity> finishFileList = StringUtils.hasText(result.getRmCheckFileId()) ? ossService.list(result.getRmCheckFileId()) : new ArrayList<>();
            int maxSize = Math.max(checkFileList.size(), finishFileList.size());
            if (maxSize > 0) {
                for (int i = 0; i < maxSize; i++) {
                    try {
                        ISlide slideTemplate7 = sourcePpt.getSlides().get(6);
                        targetPpt.getSlides().append(slideTemplate7);
                        String subTitle = StringUtils.hasText(result.getFirstCategoryName()) ? result.getFirstCategoryName() : "";
                        if (StringUtils.hasText(result.getSecondCategoryName())) {
                            subTitle += "-" + result.getSecondCategoryName();
                        }
                        if (StringUtils.hasText(result.getCategoryName())) {
                            subTitle += "-" + result.getCategoryName();
                        }
                        textMap.put("#subTitle#", subTitle);
                        textMap.put("#instruction#", StringUtils.hasText(result.getRmCheckQuestionPlacehoder())? result.getRmCheckQuestionPlacehoder() : "");
                        textMap.put("#acceptanceRemark#", StringUtils.hasText(result.getRmCheckComment())? result.getRmCheckComment() : "");
                        ISlide targetSlide = targetPpt.getSlides().get(targetPpt.getSlides().getCount() - 1);
                        replaceText(targetSlide, textMap);

                        if (!CollectionUtils.isEmpty(checkFileList)) {
                            // 插入示例图片
                            if (i < checkFileList.size()) {
                                Rectangle2D.Double left = new Rectangle2D.Double(53, 95, 406, 266);
                                OssEntity checkPic = checkFileList.get(i);
                                drawVideoOrPic(targetSlide, checkPic, left);
                            } else {
                                Rectangle2D.Double left = new Rectangle2D.Double(53, 95, 406, 266);
                                OssEntity checkPic = checkFileList.get(checkFileList.size() - 1);
                                drawVideoOrPic(targetSlide, checkPic, left);
                            }
                        }
                        if (!CollectionUtils.isEmpty(finishFileList)) {
                            // 插入完工图片
                            if (i < finishFileList.size()) {
                                Rectangle2D.Double right = new Rectangle2D.Double(502, 95, 406, 266);
                                OssEntity finishPic = finishFileList.get(i);
                                drawVideoOrPic(targetSlide, finishPic, right);
                            } else {
                                Rectangle2D.Double right = new Rectangle2D.Double(502, 95, 406, 266);
                                OssEntity finishPic = finishFileList.get(finishFileList.size() - 1);
                                drawVideoOrPic(targetSlide, finishPic, right);
                            }
                        }
                    } catch (Exception e) {
                        throw new CommonException(e.getMessage());
                    }
                }
            }
        });
    }

    private void generateQuestionSummaryPpt(List<TaskCheckResultAndReformDto> checkResultAndReforms, Presentation sourcePpt, Presentation targetPpt, Map<String, String> textMap) {
        log.info("开始生成验收问题概况ppt...");
        checkResultAndReforms.forEach(result -> {
            List<OssEntity> checkFileList = StringUtils.hasText(result.getCheckFileId()) ? ossService.list(result.getCheckFileId()) : new ArrayList<>();
            List<OssEntity> reformFileList = StringUtils.hasText(result.getReformFileId()) ? ossService.list(result.getReformFileId()) : new ArrayList<>();
            try {
                insertPicIntoPpt(sourcePpt, targetPpt, textMap, result, checkFileList, reformFileList);
            } catch (Exception e) {
                throw new CommonException(e.getMessage());
            }
        });
    }

    private boolean isImage(String fileType) {
        // 定义图片文件扩展名的集合
        Set<String> imageExtensions = new HashSet<>(Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"));
        String extension = fileType.toLowerCase();
        // 判断扩展名是否在集合中
        return imageExtensions.contains(extension);
    }

    // 定义方法，判断是否为视频格式
    private boolean isVideo(String fileType) {
        // 定义视频文件扩展名的集合
        Set<String> videoExtensions = new HashSet<>(Arrays.asList(".mp4", ".avi", ".mov", ".wmv", ".mkv", ".rmvb"));
        String extension = fileType.toLowerCase();
        // 判断扩展名是否在集合中
        return videoExtensions.contains(extension);
    }

    private void insertPicIntoPpt(Presentation sourcePpt, Presentation targetPpt, Map<String, String> textMap, TaskCheckResultAndReformDto resultAndReformDto, List<OssEntity> checkFileList, List<OssEntity> reformFileList) throws Exception {
        int checkSize = checkFileList.size();
        int reformSize = reformFileList.size();
        int maxSize = Math.max(reformSize, checkSize);
        if (maxSize < 5 && maxSize > 0) { // todo 考虑如果 maxSize = 0, 待确认是否要生成 PPT 页面
            handlePptWithLessThanFivePics(sourcePpt, targetPpt, textMap, resultAndReformDto, checkFileList, reformFileList);
        } else {
            for (int i = 0; i < maxSize; i += 4) {
                int endIndex = Math.min(i + 4, maxSize);
                // 判断列表是否越界
                List<OssEntity> checkSubList = checkFileList.size() < endIndex ? checkFileList : checkFileList.subList(i, endIndex);
                List<OssEntity> reformSubList = reformFileList.size() < endIndex ? reformFileList : reformFileList.subList(i, endIndex);
                // 递归调用
                insertPicIntoPpt(sourcePpt, targetPpt, textMap, resultAndReformDto, checkSubList, reformSubList);
            }
        }
    }

    private void handlePptWithLessThanFivePics(Presentation sourcePpt, Presentation targetPpt, Map<String, String> textMap, TaskCheckResultAndReformDto resultAndReformDto, List<OssEntity> checkFileList, List<OssEntity> reformFileList) throws Exception {
        // 处理文本
        ISlide slideTemplate5 = sourcePpt.getSlides().get(4);
        targetPpt.getSlides().append(slideTemplate5);
        String subTitle = resultAndReformDto.getFirstCategoryName() + "-" + resultAndReformDto.getSecondCategoryName() + "-" +  resultAndReformDto.getCategoryName();
        if (StringUtils.hasText(resultAndReformDto.getRoomNum())) {
            subTitle = subTitle + "-" + resultAndReformDto.getRoomNum();
        }
        textMap.put("#subTitle#", subTitle);
        textMap.put("#instruction#", StringUtils.hasText(resultAndReformDto.getCheckComment())? resultAndReformDto.getCheckComment() : "");
        textMap.put("#improvement#", StringUtils.hasText(resultAndReformDto.getReformComment())? resultAndReformDto.getReformComment() : "");
        ISlide targetSlide = targetPpt.getSlides().get(targetPpt.getSlides().getCount() - 1);
        replaceText(targetSlide, textMap);
        // 处理图片
        // 绘制整改前的图形
        handleCheckPptSection(checkFileList, targetSlide);
        // 绘制整改后的图形
        handleReformPptSection(reformFileList, targetSlide);
    }

    private void handleCheckPptSection(List<OssEntity> checkFileList, ISlide targetSlide) throws Exception {
        if (checkFileList.size() == 1) {
            Rectangle2D.Double left = new Rectangle2D.Double(53, 95, 406, 266);
            OssEntity checkPic = checkFileList.get(0);
            drawVideoOrPic(targetSlide, checkPic, left);
        }
        if (checkFileList.size() == 2) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(53, 98, 200, 120);
            Rectangle2D.Double rightDown = new Rectangle2D.Double(258, 230, 200, 120);
            OssEntity leftUpPic = checkFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity rightDownPic = checkFileList.get(1);
            drawVideoOrPic(targetSlide, rightDownPic, rightDown);
        }
        if (checkFileList.size() == 3) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(53, 98, 200, 120);
            Rectangle2D.Double leftDown = new Rectangle2D.Double(53, 230, 200, 120);
            Rectangle2D.Double rightUp = new Rectangle2D.Double(258, 98, 200, 120);
            OssEntity leftUpPic = checkFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity leftDownPic = checkFileList.get(1);
            drawVideoOrPic(targetSlide, leftDownPic, leftDown);

            OssEntity rightUpPic = checkFileList.get(2);
            drawVideoOrPic(targetSlide, rightUpPic, rightUp);
        }
        if (checkFileList.size() == 4) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(53, 98, 200, 120);
            Rectangle2D.Double leftDown = new Rectangle2D.Double(53, 230, 200, 120);
            Rectangle2D.Double rightUp = new Rectangle2D.Double(258, 98, 200, 120);
            Rectangle2D.Double rightDown = new Rectangle2D.Double(258, 230, 200, 120);
            OssEntity leftUpPic = checkFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity leftDownPic = checkFileList.get(1);
            drawVideoOrPic(targetSlide, leftDownPic, leftDown);

            OssEntity rightUpPic = checkFileList.get(2);
            drawVideoOrPic(targetSlide, rightUpPic, rightUp);

            OssEntity rightDownPic = checkFileList.get(3);
            drawVideoOrPic(targetSlide, rightDownPic, rightDown);
        }
    }

    private void drawVideoOrPic(ISlide targetSlide, OssEntity ossEntity, Rectangle2D.Double rectangle) throws Exception {
        if (ossEntity.getStorageType() == EnumStorageType.LOCAL.getIndex()) {
            if (StringUtils.hasText(ossEntity.getType()) && isVideo(ossEntity.getType())) {
                targetSlide.getShapes().appendVideoMedia(ossEntity.getPath(), rectangle);
            } else {
                targetSlide.getShapes().appendEmbedImage(ShapeType.RECTANGLE, ossEntity.getPath(), rectangle);
            }
        } else {
            var url = AliyunOssUtils.generatePresignedUrl(ossEntity.getName());
            InputStream inputStream = new URL(url).openStream();
            if (StringUtils.hasText(ossEntity.getType()) && isVideo(ossEntity.getType())) {
                targetSlide.getShapes().appendVideoMedia(inputStream, rectangle);
            } else {
                targetSlide.getShapes().appendEmbedImage(ShapeType.RECTANGLE, inputStream, rectangle);
            }
            if (inputStream!= null) {
                inputStream.close();
            }
        }
    }

    private void handleReformPptSection(List<OssEntity> reformFileList, ISlide targetSlide) throws Exception {
        if (reformFileList.size() == 1) {
            Rectangle2D.Double right = new Rectangle2D.Double(502, 95, 406, 266);
            OssEntity reformPic = reformFileList.get(0);
            drawVideoOrPic(targetSlide, reformPic, right);
        }
        if (reformFileList.size() == 2) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(503, 98, 200, 120);
            Rectangle2D.Double rightDown = new Rectangle2D.Double(708, 230, 200, 120);
            OssEntity leftUpPic = reformFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity rightDownPic = reformFileList.get(1);
            drawVideoOrPic(targetSlide, rightDownPic, rightDown);
        }
        if (reformFileList.size() == 3) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(503, 98, 200, 120);
            Rectangle2D.Double leftDown = new Rectangle2D.Double(503, 230, 200, 120);
            Rectangle2D.Double rightUp = new Rectangle2D.Double(708, 98, 200, 120);
            OssEntity leftUpPic = reformFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity leftDownPic = reformFileList.get(1);
            drawVideoOrPic(targetSlide, leftDownPic, leftDown);

            OssEntity rightUpPic = reformFileList.get(2);
            drawVideoOrPic(targetSlide, rightUpPic, rightUp);
        }
        if (reformFileList.size() == 4) {
            Rectangle2D.Double leftUp = new Rectangle2D.Double(503, 98, 200, 120);
            Rectangle2D.Double leftDown = new Rectangle2D.Double(503, 230, 200, 120);
            Rectangle2D.Double rightUp = new Rectangle2D.Double(708, 98, 200, 120);
            Rectangle2D.Double rightDown = new Rectangle2D.Double(708, 230, 200, 120);
            OssEntity leftUpPic = reformFileList.get(0);
            drawVideoOrPic(targetSlide, leftUpPic, leftUp);

            OssEntity leftDownPic = reformFileList.get(1);
            drawVideoOrPic(targetSlide, leftDownPic, leftDown);

            OssEntity rightUpPic = reformFileList.get(2);
            drawVideoOrPic(targetSlide, rightUpPic, rightUp);

            OssEntity rightDownPic = reformFileList.get(3);
            drawVideoOrPic(targetSlide, rightDownPic, rightDown);
        }
    }

    private static void replaceText(ISlide slide, Map<String, String> textMap) throws DocumentEditException {
        for (Object shape : slide.getShapes()) {
            if (shape instanceof IAutoShape) {
                for (Object paragraph : ((IAutoShape) shape).getTextFrame().getParagraphs()) {
                    ParagraphEx paragraphEx = (ParagraphEx)paragraph;
                    for (String key : textMap.keySet()) {
                        if (paragraphEx.getText().contains(key)) {
                            paragraphEx.setText(paragraphEx.getText().replace(key, textMap.get(key)));
                        }
                    }
                }
            }

            if (shape instanceof ITable) {
                ITable table = (ITable) shape;
                int rows = table.getTableRows().getCount();
                int cols = table.getColumnsList().getCount();
                for (int row = 0; row < rows; row++) {
                    for (int col = 0; col < cols; col++) {
                        if (StringUtils.hasText(table.get(col, row).getTextFrame().getText())) {
                            if (textMap.containsKey(table.get(col, row).getTextFrame().getText())) {
                                String text = textMap.get(table.get(col, row).getTextFrame().getText());
                                table.get(col, row).getTextFrame().setText(StringUtils.hasText(text) ? text : "");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 上传 PPT 到 OSS
     */
    private void saveToOss(byte[] data, FileTaskEntity entity) {
        Path localPath = Path.of(storagePath, entity.getFileName());
        // 构建本地文件路径 将data写入本地文件
        File localFile = localPath.toFile();
        try (FileOutputStream outputStream = new FileOutputStream(localFile)) {
            outputStream.write(data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EnumStorageType enumStorageType = EnumStorageType.fromName(storageType);
        if (enumStorageType == EnumStorageType.LOCAL) {
            entity.setFilePath(localFile.getPath());
        } else {
            // 存储到 OSS
            String path = AliyunOssUtils.putObject(entity.getFileName(), data);
            entity.setFilePath(path);
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(FileTaskEntity fileEntity, int status, Boolean isNeedUpdateOss) {
        if (isNeedUpdateOss) {
            OssEntity ossEntity = new OssEntity();
            ossEntity.setId(IdUtils.nextSnowFlakeId());
            ossEntity.setOssId(ossEntity.getId());
            ossEntity.setName(fileEntity.getFileName());
            ossEntity.setOriginalName(fileEntity.getOriginalName());
            ossEntity.setType(fileEntity.getFileType());
            ossEntity.setSize(fileEntity.getFileSize());
            ossEntity.setPath(fileEntity.getFilePath());
            ossEntity.setStorageType(EnumStorageType.fromName(storageType).getIndex());
            ossEntity.setIsEnabled(true);
            ossEntity.setIsDeleted(false);
            ossEntity.setCreator(String.valueOf(1L));
            ossEntity.setCreatorAccount("admin");
            ossEntity.setCreatorName("系统");
            ossEntity.setCreateTime(LocalDateTime.now());

            fileEntity.setOssId(ossEntity.getId());
            fileEntity.setState(status);
            Db.tx(() -> {
                ossMapper.insert(ossEntity);
                fileTaskMapper.update(fileEntity, true);
                return true;
            });
        } else {
            fileEntity.setState(status);
            fileEntity.setModifier(String.valueOf(1L));
            fileEntity.setModifierAccount("xxl-job");
            fileEntity.setModifierName("xxl-job");
            fileEntity.setModifiedTime(LocalDateTime.now());
            fileTaskMapper.update(fileEntity, true);
        }
    }

    private Mono<QueryWrapper> createQueryWrapper(FileTaskQueryDto queryModel) {
        return UserUtils.get().flatMap(currentUser -> {
            QueryWrapper queryWrapper = new QueryWrapper();
            if (StringUtils.hasText(queryModel.getOriginalName())) {
                queryWrapper.like(FileTaskEntity::getOriginalName, queryModel.getOriginalName());
            }
            if (Objects.nonNull(queryModel.getState())) {
                queryWrapper.eq(FileTaskEntity::getState, queryModel.getState());
            }
            if (Objects.nonNull(currentUser.getUserId())) {
                queryWrapper.eq(FileTaskEntity::getCreator, currentUser.getUserId());
            }
            queryWrapper.orderBy(FileTaskEntity::getCreateTime, false);
            return Mono.just(queryWrapper);
        });
    }

    public Integer delFileTask(List<String> ids) {
        // 判断是否都是待验收的
        return fileTaskMapper.deleteBatchByIds(ids);
    }

}
