package com.atour.cms.audit.service.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.atour.cms.audit.constant.CryptoKey;
import com.atour.cms.audit.constant.EnumDirectoryType;
import com.atour.cms.audit.constant.EnumSynStatus;
import com.atour.cms.audit.constant.EnumUserType;
import com.atour.cms.audit.constant.acms.EnumProjectNode;
import com.atour.cms.audit.constant.form.EnumFormStatus;
import com.atour.cms.audit.constant.question.EnumCategoryPackageType;
import com.atour.cms.audit.constant.question.EnumQuestionClassify;
import com.atour.cms.audit.constant.question.EnumQuestionType;
import com.atour.cms.audit.constant.system.EnumModule;
import com.atour.cms.audit.constant.system.EnumRole;
import com.atour.cms.audit.constant.task.*;
import com.atour.cms.audit.dto.PageInfo;
import com.atour.cms.audit.dto.PageQuery;
import com.atour.cms.audit.dto.Response;
import com.atour.cms.audit.dto.form.FormUserQueryDto;
import com.atour.cms.audit.dto.task.*;
import com.atour.cms.audit.dto.user.CurrentUser;
import com.atour.cms.audit.entity.BaseEntity;
import com.atour.cms.audit.entity.basedata.DirectoryEntity;
import com.atour.cms.audit.entity.basedata.ProjectMemberEntity;
import com.atour.cms.audit.entity.system.*;
import com.atour.cms.audit.entity.task.*;
import com.atour.cms.audit.exception.CommonException;
import com.atour.cms.audit.mapper.task.*;
import com.atour.cms.audit.service.acms.AcmsProjectService;
import com.atour.cms.audit.service.basedata.DirectoryService;
import com.atour.cms.audit.service.basedata.ProjectService;
import com.atour.cms.audit.service.system.MessageService;
import com.atour.cms.audit.service.system.OssService;
import com.atour.cms.audit.service.system.UserService;
import com.atour.cms.audit.util.*;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.atour.cms.audit.entity.form.FormCategoryEntity;
import com.atour.cms.audit.entity.form.FormCategoryQuestionEntity;
import com.atour.cms.audit.entity.form.FormEntity;
import com.atour.cms.audit.entity.question.QuestionEntity;
import com.atour.cms.audit.mapper.form.FormCategoryMapper;
import com.atour.cms.audit.mapper.form.FormMapper;
import com.atour.cms.audit.service.common.CommonService;
import com.atour.cms.audit.service.form.FormService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * TaskService
 *
 * <AUTHOR>
 */
@Service
public class TaskService {
    @Resource
    private TaskListMapper taskListMapper;
    @Resource
    private FormCategoryMapper formCategoryMapper;
    @Resource
    private TaskCheckResultMapper taskCheckResultMapper;
    @Resource
    private TaskCheckResultRoomMapper taskCheckResultRoomMapper;
    @Resource
    private TaskReformResultMapper taskReformResultMapper;
    @Resource
    private TaskHistoryMapper taskHistoryMapper;
    @Resource
    private TaskCheckLogMapper taskCheckLogMapper;
    @Resource
    private TaskHistoryReceiptMapper taskHistoryReceiptMapper;
    @Resource
    private TaskScoreMapper taskScoreMapper;
    @Resource
    private FormMapper formMapper;
    @Resource
    private TaskCheckRoomMapper taskCheckRoomMapper;
    @Resource
    private RoomMapper roomMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private FormService formService;
    @Resource
    private UserService userService;
    @Resource
    private DirectoryService directoryService;
    @Resource
    private OssService ossService;
    @Resource
    private MessageService messageService;
    @Resource
    private ProjectService projectService;
    @Resource
    private AcmsProjectService acmsProjectService;
    /**
     * 最少房间数
     */
    @Value("${room.check.min}")
    private Integer roomCheckMinNums;
    /**
     * 最少比例
     */
    @Value("${room.check.ratio}")
    private Double roomCheckRatio;


    /**
     * 派案
     *
     * @param body 请求
     * @return 结果
     */
    public Mono<Response<String>> assignTask(TaskListEntity body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    generateTaskList(body.getProjectId(), currentUser, body.getFormId());
                    return RestUtils.success("成功");
                });
    }

    /**
     * 分页查询
     *
     * @param pageQuery 查询条件
     * @return 结果
     */
    public Mono<Response<PageInfo<TaskListEntity>>> getTaskPage(PageQuery<TaskListQueryDto> pageQuery) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    Page<TaskListEntity> page = taskListMapper.paginateAs(pageQuery.getCurrent(), pageQuery.getPageSize(), createQueryWrapper(pageQuery.getQueryModel(), currentUser), TaskListEntity.class);
                    return RestUtils.success(RestUtils.buildPage(page));
                });
    }

    /**
     * 构造查询条件
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    private QueryWrapper createQueryWrapper(TaskListQueryDto queryDto, CurrentUser currentUser) {
        QueryWrapper queryWrapper = new QueryWrapper();
        // 只要当前用户是项目成员中的任一成员，都可以看到
        if (currentUser.getUserType() != EnumUserType.Admin.getIndex()) {
            queryWrapper.and(String.format("exists(select 1 " +
                    "from t_bd_project_member " +
                    "where t_bd_project_member.project_id = t_audit_task_list.project_id " +
                    "and t_bd_project_member.user_id = %s)", currentUser.getUserId()));
        }
        if (Objects.nonNull(queryDto.getBrandId())) {
            queryWrapper.eq(TaskListEntity::getBrandId, queryDto.getBrandId());
        }
        if (Objects.nonNull(queryDto.getMarketId())) {
            queryWrapper.eq(TaskListEntity::getMarketId, queryDto.getMarketId());
        }
        if (StringUtils.hasLength(queryDto.getProjectName())) {
            queryWrapper.like(TaskListEntity::getProjectName, queryDto.getProjectName());
        }
        if (Objects.nonNull(queryDto.getBrandIds()) && queryDto.getBrandIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getBrandId, queryDto.getBrandIds());
        }
        if (Objects.nonNull(queryDto.getMarketIds()) && queryDto.getMarketIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getMarketId, queryDto.getMarketIds());
        }
        if (Objects.nonNull(queryDto.getPmUserIds()) && queryDto.getPmUserIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getPmUserId, queryDto.getPmUserIds());
        }
        if (Objects.nonNull(queryDto.getVersionIds()) && queryDto.getVersionIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getProductVersionId, queryDto.getVersionIds());
        }
        if (Objects.nonNull(queryDto.getStatusIds()) && queryDto.getStatusIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getStatus, queryDto.getStatusIds());
        }
        if (Objects.nonNull(queryDto.getCheckUserIds()) && queryDto.getCheckUserIds().size() > 0) {
            queryWrapper.in(TaskListEntity::getCheckUserId, queryDto.getCheckUserIds());
        }
        if (Objects.nonNull(queryDto.getStartTimes()) && queryDto.getStartTimes().length > 1) {
            queryWrapper.between(TaskListEntity::getStartTime, queryDto.getStartTimes()[0], queryDto.getStartTimes()[1]);
        }
        // 关键字
        if (StringUtils.hasLength(queryDto.getKeyWord())) {
            queryWrapper.and(QueryMethods.column(TaskListEntity::getProjectName).like(queryDto.getKeyWord())
                    .or(QueryMethods.column(TaskListEntity::getStoreName).like(queryDto.getKeyWord()))
                    .or(QueryMethods.column(TaskListEntity::getProjectNo).like(queryDto.getKeyWord())));
        }
        if (Objects.nonNull(queryDto.getSort()) && queryDto.getSort().size() > 0) {
            for (var sort : queryDto.getSort()) {
                queryWrapper.orderBy(sort, false);
            }
        }
        queryWrapper.orderBy(TaskListEntity::getCreateTime, false);
        return queryWrapper;
    }

    /**
     * 导出
     *
     * @param queryDto 查询条件
     * @return 结果
     */
    public Mono<ResponseEntity<Flux<DataBuffer>>> taskExport(TaskListQueryDto queryDto) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    Set<String> excludeColumnFiledNames = new HashSet<>();
                    List<TaskListEntity> list = taskListMapper.selectListByQueryAs(createQueryWrapper(queryDto, currentUser), TaskListEntity.class);
                    AtomicLong no = new AtomicLong(1);
                    list.forEach(item -> item.setNo(no.getAndIncrement()));
                    return commonService.asyncExport(list, TaskListEntity.class, "验收列表", "验收列表.xlsx", excludeColumnFiledNames);
                });
    }

    /**
     * 删除验收任务
     *
     * @param ids 集合
     * @return 结果
     */
    public Integer delTask(List<String> ids) {
        // 判断是否都是待验收的
        return taskListMapper.deleteBatchByIds(ids);
    }

    /**
     * 获取验收任务
     *
     * @param id ID
     * @return 结果
     */
    public Mono<TaskListEntity> getTask(String id) {
        var task = taskListMapper.selectOneById(id);
        if (Objects.isNull(task)) {
            throw new CommonException("任务不存在");
        }
        var form = formMapper.selectOneById(task.getFormId());
        if (Objects.isNull(form)) {
            throw new CommonException("任务未关联表单");
        }
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    // 查看当前用户是否在项目的成员中
                    if (currentUser.getUserType() != EnumUserType.Admin.getIndex()) {
                        boolean hasPermission = projectService.hasPrivilege(task.getProjectId(), currentUser.getUserId());
                        if (!hasPermission) {
                            return RestUtils.throwError("没有查看该任务的权限");
                        }
                    }
                    return Mono.just(task);
                });
    }

    /**
     * 获取验收任务
     *
     * @param id ID
     * @return 结果
     */
    public Mono<TaskHistoryEntity> getTaskHistory(String id) {
        var history = taskHistoryMapper.selectOneById(id);
        if (Objects.isNull(history)) {
            throw new CommonException("验收记录不存在");
        }
        var task = taskListMapper.selectOneById(history.getTaskId());
        if (Objects.isNull(task)) {
            throw new CommonException("验收任务不存在");
        }
        var form = formMapper.selectOneById(history.getFormId());
        if (Objects.isNull(form)) {
            throw new CommonException("任务未关联表单");
        }
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    // 查看当前用户是否在项目的成员中
                    if (currentUser.getUserType() != EnumUserType.Admin.getIndex()) {
                        boolean hasPermission = projectService.hasPrivilege(task.getProjectId(), currentUser.getUserId());
                        if (!hasPermission) {
                            return RestUtils.throwError("没有查看该任务的权限");
                        }
                    }
                    return Mono.just(history);
                });
    }

    /**
     * 获取验收问题列表
     *
     * @param body 验收任务历史ID
     * @return 结果
     */
    public List<TaskCheckResultEntity> getCheckQuestionList(TaskCheckResultEntity body) {
        return taskCheckResultMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskCheckResultEntity::getHistoryId, body.getHistoryId())
                .eq(TaskCheckResultEntity::getCategoryId, body.getCategoryId()));
    }

    /**
     * 获取验收问题占比
     *
     * @param historyId 验收任务历史ID
     * @return 结果
     */
    public TaskCheckResultNumDto getCheckQuestionNum(String historyId) {
        var result = new TaskCheckResultNumDto();
        result.setTotal(taskCheckResultMapper.selectCountByQuery(QueryWrapper.create()
                .eq(TaskCheckResultEntity::getHistoryId, historyId)));
        // 没有填写验收结果的数据
        result.setYesNum(taskCheckResultMapper.selectCountByQuery(QueryWrapper.create()
                .eq(TaskCheckResultEntity::getHistoryId, historyId)
                .isNotNull(TaskCheckResultEntity::getModifiedTime)));
        return result;
    }

    /**
     * 新增验收问题
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<TaskCheckResultEntity>> addCheckQuestion(TaskCheckResultEntity body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    body.setQuestionType(EnumQuestionType.Diy.getIndex());
                    body.setQuestionCommentRequired(3);
                    body.setQuestionPhotoRequired(3);
                    body.buildCreateInfo(currentUser);
                    taskCheckResultMapper.insert(body, true);
                    return RestUtils.success(body);
                });
    }

    /**
     * 删除验收问题
     *
     * @param ids 集合
     * @return 结果
     */
    public Integer delCheckQuestion(List<String> ids) {
        return taskCheckResultMapper.deleteBatchByIds(ids);
    }

    /**
     * 保存验收问题
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<String>> saveCheckQuestion(TaskCheckSaveDto body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var list = body.getCheckResultList();
                    if (body.getIsRoomCheck()) {
                        var roomList = new ArrayList<TaskCheckResultRoomEntity>();
                        for (var item : list) {
                            var room = new TaskCheckResultRoomEntity();
                            BeanUtils.copyProperties(item, room);
                            room.buildModifiedInfo(currentUser);
                            roomList.add(room);
                        }
                        // 更新房间对应的状态
                        if (Objects.nonNull(body.getCheckRoom())) {
                            body.getCheckRoom().buildModifiedInfo(currentUser);
                        }
                        var checkResult = new TaskCheckResultEntity();
                        checkResult.buildModifiedInfo(currentUser);
                        Db.tx(() -> {
                            if (!roomList.isEmpty()) {
                                Db.executeBatch(roomList, 100, TaskCheckResultRoomMapper.class, (mapper, entity) -> mapper.update(entity, true));
                                if (Objects.nonNull(body.getCheckRoom())) {
                                    taskCheckRoomMapper.update(body.getCheckRoom(), true);
                                }
                                taskCheckResultMapper.updateByQuery(checkResult, QueryWrapper.create()
                                        .in(TaskCheckResultEntity::getId, roomList.stream().map(TaskCheckResultRoomEntity::getCheckId).toList()));
                            }
                            return true;
                        });
                    } else {
                        var categoryList = new ArrayList<TaskCheckResultEntity>();
                        for (var item : list) {
                            var taskCheckResult = new TaskCheckResultEntity();
                            BeanUtils.copyProperties(item, taskCheckResult);
                            taskCheckResult.buildModifiedInfo(currentUser);
                            categoryList.add(taskCheckResult);
                        }
                        if (!categoryList.isEmpty()) {
                            Db.executeBatch(categoryList, 100, TaskCheckResultMapper.class, (mapper, entity) -> mapper.update(entity, true));
                        }
                    }
                    return RestUtils.success("成功");
                });
    }

    /**
     * 获取整改问题
     *
     * @param historyId 验收任务ID
     * @return 结果
     */
    public List<TaskReformResultEntity> getReformQuestionList(String historyId) {
        return taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskReformResultEntity::getHistoryId, historyId));
    }

    /**
     * 通知整改
     *
     * @param task 验收任务
     * @return 结果
     */
    public Mono<String> noticeReform(TaskListEntity task) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq(TaskReformResultEntity::getTaskId, task.getId());
                    TaskReformResultEntity updateEntity = new TaskReformResultEntity();
                    // 设置状态为待整改
                    updateEntity.setStatus(EnumReformStatus.Reforming.getIndex());
                    updateEntity.buildModifiedInfo(currentUser);
                    // 设置Task为整改中
                    var originTask = taskListMapper.selectOneById(task.getId());
                    if (Objects.isNull(originTask) || originTask.getStatus() != EnumTaskStatus.PENDING_REFORM.getIndex()) {
                        return RestUtils.throwError("任务不存在或者状态不正确");
                    }
                    task.setStatus(EnumTaskStatus.IN_REFORM.getIndex());
                    task.buildModifiedInfo(currentUser);
                    if (Objects.nonNull(task.getReformUserId())) {
                        var reformUser = userService.selectUserByQuery(QueryWrapper.create()
                                .eq(UserEntity::getId, task.getReformUserId())
                                .eq(UserEntity::getIsEnabled, true));
                        if (Objects.isNull(reformUser)) {
                            return RestUtils.throwError("整改人不存在");
                        }
                        task.setReformUserName(reformUser.getCnName());
                        task.setReformUserMobile(CryptoUtils.aesDecryptHutool(reformUser.getPhone(), CryptoKey.FIS_AES_KEY));
                    }
                    if (Objects.nonNull(task.getReviewUserId())) {
                        var reviewUser = userService.selectUserByQuery(QueryWrapper.create()
                                .eq(UserEntity::getId, task.getReviewUserId())
                                .eq(UserEntity::getIsEnabled, true));
                        if (Objects.isNull(reviewUser)) {
                            return RestUtils.throwError("复核人不存在");
                        }
                        task.setReviewUserName(reviewUser.getCnName());
                        task.setReviewUserMobile(CryptoUtils.aesDecryptHutool(reviewUser.getPhone(), CryptoKey.FIS_AES_KEY));
                    }
                    Db.tx(() -> {
                        taskListMapper.update(task, true);
                        taskReformResultMapper.updateByQuery(updateEntity, queryWrapper);
                        return true;
                    });
                    // 返回更新结果
                    return Mono.just("成功");
                });

    }

    /**
     * 获取验收问题详情
     *
     * @param body 请求体
     * @return 结果
     */
    public BaseEntity getTaskCheckResult(TaskReformResultEntity body) {
        return body.getIsRoomCheck() ? taskCheckResultRoomMapper.selectOneById(body.getCheckId())
                : taskCheckResultMapper.selectOneById(body.getCheckId());
    }

    /**
     * 获取整改问题详情
     *
     * @param id 问题ID
     * @return 结果
     */
    public Mono<TaskReformResultEntity> getTaskReformResult(String id) {
        var reform = taskReformResultMapper.selectOneById(id);
        if (Objects.isNull(reform)) {
            throw new CommonException("整改不存在");
        }
        var task = taskListMapper.selectOneById(reform.getTaskId());
        if (Objects.isNull(task)) {
            throw new CommonException("任务不存在");
        }
        var form = formMapper.selectOneById(task.getFormId());
        if (Objects.isNull(form)) {
            throw new CommonException("任务未关联表单");
        }
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    // 查看当前用户是否在项目的成员中
                    if (currentUser.getUserType() != EnumUserType.Admin.getIndex()) {
                        boolean hasPermission = projectService.hasPrivilege(task.getProjectId(), currentUser.getUserId());
                        if (!hasPermission) {
                            return RestUtils.throwError("没有查看该任务的权限");
                        }
                    }
                    return Mono.just(reform);
                });
    }

    /**
     * 保存整改问题
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<String>> saveTaskReformResult(TaskReformSaveDto body) {
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    var reform = body.getReform();
                    reform.buildModifiedInfo(currentUser);
                    if (Objects.nonNull(body.getHistoryStatus()) && body.getHistoryStatus() != EnumTaskStatus.IN_REFORM.getIndex()) {
                        // 设置history和task为整改中
                        var history = taskHistoryMapper.selectOneById(body.getHistoryId());
                        var task = taskListMapper.selectOneById(history.getTaskId());
                        history.setStatus(EnumTaskStatus.IN_REFORM.getIndex());
                        history.buildModifiedInfo(currentUser);
                        task.setStatus(EnumTaskStatus.IN_REFORM.getIndex());
                        task.buildModifiedInfo(currentUser);
                        Db.tx(() -> {
                            taskReformResultMapper.update(reform, true);
                            taskHistoryMapper.update(history, true);
                            taskListMapper.update(task, true);
                            return true;
                        });
                    } else {
                        // 查询代办任务，完成任务
                        var message = messageService.getBusinessTask(reform.getId(), currentUser.getUserId(), EnumModule.ReformScheme.getIndex());
                        Db.tx(() -> {
                            if (Objects.nonNull(message)) {
                                messageService.finishMessage(message.getId(), currentUser, false);
                            }
                            taskReformResultMapper.update(reform, true);
                            return true;
                        });
                    }
                    return RestUtils.success("成功");
                });
    }

    /**
     * 复核问题
     *
     * @param reviewSaveDto 请求体
     * @return 结果
     */
    public Mono<Response<String>> saveTaskReviewResult(ReviewSaveDto reviewSaveDto) {
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    var history = taskHistoryMapper.selectOneById(reviewSaveDto.getHistoryId());
                    if (Objects.isNull(history)) {
                        return RestUtils.throwError("未找到对应的验收记录");
                    }
                    var task = taskListMapper.selectOneById(history.getTaskId());
                    if (Objects.isNull(task)) {
                        return RestUtils.throwError("未找到对应的验收任务");
                    }
                    var list = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                            .eq(TaskReformResultEntity::getHistoryId, reviewSaveDto.getHistoryId())
                            .in(TaskReformResultEntity::getId, reviewSaveDto.getList().stream().map(ReviewSaveDto.ReviewItem::getId).toList()));
                    if (list.isEmpty()) {
                        return RestUtils.throwError("未找到对应的整改记录");
                    }
                    // 判断选中的复核问题的状态，是否都是待复核，如果不是，则返回错误
                    if (list.stream().anyMatch(item -> item.getStatus() != EnumReformStatus.Reviewing.getIndex())) {
                        return RestUtils.throwError("只能审核待复核的数据");
                    }
                    var updateList = new ArrayList<TaskReformResultEntity>();
                    var addApproveLogList = new ArrayList<ApproveLogEntity>();
                    var messageList = new ArrayList<MessageEntity>();
                    var projectMemberMap = projectService.getProjectMemberList(task.getProjectId())
                            .stream().collect(Collectors.toMap(ProjectMemberEntity::getRoleCode, Function.identity()));
                    for (var item : list) {
                        var update = new TaskReformResultEntity();
                        update.setId(item.getId());
                        update.setReviewUserId(currentUser.getUserId());
                        update.setReviewTime(LocalDateTime.now());
                        update.setReviewUserName(currentUser.getUserName());
                        update.setReviewResult(reviewSaveDto.getType().equals(1) ? EnumReviewResult.Reject.getIndex() : EnumReviewResult.Accept.getIndex());
                        update.setReviewComment(reviewSaveDto.getComment());
                        update.buildModifiedInfo(currentUser);
                        updateList.add(update);
                        // 记录审批日志
                        var approveLog = messageService.createApproveLogBuilder()
                                .action(reviewSaveDto.getType().equals(1) ? "复核退回" : "复核通过")
                                .comment(reviewSaveDto.getComment())
                                .businessId(item.getId())
                                .businessName(EnumModule.Review.getName())
                                .roleCode(reviewSaveDto.getRoleCode())
                                .user(currentUser)
                                .build();
                        addApproveLogList.add(approveLog);
                        //退回时更新状态为待整改
                        if (reviewSaveDto.getType().equals(1)) {
                            update.setStatus(EnumReformStatus.Reforming.getIndex());
                            var leaderId = IdUtils.nextSnowFlakeId();
                            var pmId = IdUtils.nextSnowFlakeId();
                            // 发送任务给现长和项目经理
                            var projectMemberLeader = projectMemberMap.get(EnumRole.OPERATION_LEADER.getCode());
                            var projectMemberManager = projectMemberMap.get(EnumRole.DEVELOPMENT_MANAGER.getCode());
                            var messageEntity = messageService.createTaskBuilder(true)
                                    .title("竣工验收整改任务")
                                    .content(String.format("您有一个项目%s-%s的验收整改被退回，请及时处理", history.getStoreCode(), history.getStoreName()))
                                    .url(String.format("/audit-mobile-app/audit/reform/%s?tab=3", item.getId()))
                                    .businessId(item.getId())
                                    .businessName("竣工验收")
                                    .moduleId(EnumModule.Reform.getIndex())
                                    .moduleName(EnumModule.Reform.getName())
                                    .projectMember(projectMemberLeader)
                                    .id(leaderId)
                                    .linkId(pmId)
                                    .parentBusinessId(history.getId())
                                    .createUser(currentUser)
                                    .build();
                            messageList.add(messageEntity);
                            messageEntity = messageService.createTaskBuilder(true)
                                    .title("竣工验收整改任务")
                                    .content(String.format("您有一个项目%s-%s的验收整改被退回，请及时处理", history.getStoreCode(), history.getStoreName()))
                                    .url(String.format("/audit-mobile-app/audit/reform/%s", item.getId()))
                                    .businessId(item.getId())
                                    .businessName("竣工验收")
                                    .moduleId(EnumModule.Reform.getIndex())
                                    .moduleName(EnumModule.Reform.getName())
                                    .projectMember(projectMemberManager)
                                    .createUser(currentUser)
                                    .id(pmId)
                                    .parentBusinessId(history.getId())
                                    .build();
                            messageList.add(messageEntity);
                        }
                    }

                    Db.tx(() -> {
                        Db.executeBatch(updateList, 100, TaskReformResultMapper.class, (mapper, entity) -> mapper.update(entity, true));
                        // 保存审批记录
                        messageService.addApproveLog(addApproveLogList);
                        // 更新子任务
                        var subMessageIds = reviewSaveDto.getList().stream()
                                .map(ReviewSaveDto.ReviewItem::getMessageId)
                                .toList();
                        messageService.finishMessage(subMessageIds, currentUser, false);
                        // 如果全部批完,更新状态
                        if (messageService.hasSubApproved(history.getId(), EnumModule.Review.getIndex(), currentUser.getUserId(), reviewSaveDto.getRoleCode())) {
                            // 如果是退回
                            if (reviewSaveDto.getType().equals(1)) {
                                // 更新history和task的状态为待整改
                                var updateHistory = new TaskHistoryEntity();
                                updateHistory.setId(history.getId());
                                updateHistory.setStatus(EnumTaskStatus.PENDING_REFORM.getIndex());
                                updateHistory.buildModifiedInfo(currentUser);
                                var updateTask = new TaskListEntity();
                                updateTask.setId(task.getId());
                                updateTask.setStatus(EnumTaskStatus.PENDING_REFORM.getIndex());
                                updateTask.buildModifiedInfo(currentUser);
                                taskHistoryMapper.update(updateHistory, true);
                                taskListMapper.update(updateTask, true);
                            }
                        }
                        // 复核时判断
                        if (reviewSaveDto.getType().equals(2)) {
                            // 判断整改是否都复核通过了
                            var hasAllReviewAccepted = hasAllReviewAccepted(history.getId(), currentUser.getUserId());
                            if (hasAllReviewAccepted) {
                                // 判断当前处于哪个角色的审批
                                if (reviewSaveDto.getRoleCode().equals(EnumRole.DELIVERY_LEADER.getCode())) {
                                    // 设置状态为整改完成
                                    history.setStatus(EnumTaskStatus.REFORM_APPROVED.getIndex());
                                    history.buildModifiedInfo(currentUser);
                                    task.setStatus(EnumTaskStatus.REFORM_APPROVED.getIndex());
                                    task.setReviewTime(LocalDateTime.now());
                                    task.setReviewUserMobile(currentUser.getMobile());
                                    task.setReviewUserId(currentUser.getUserId());
                                    task.setReviewUserName(currentUser.getUserName());
                                    task.setReviewUserNickName(currentUser.getUserName());
                                    task.buildModifiedInfo(currentUser);
                                    // 设置整改项的状态为已复核
                                    var updateReform = new TaskReformResultEntity();
                                    updateReform.setStatus(EnumReformStatus.Finished.getIndex());
                                    updateReform.setReviewUserId(currentUser.getUserId());
                                    updateReform.setReviewTime(LocalDateTime.now());
                                    updateReform.setReviewUserName(currentUser.getUserName());
                                    updateReform.setReviewResult(EnumReviewResult.Accept.getIndex());
                                    updateReform.setReviewComment("已复核");
                                    updateReform.buildModifiedInfo(currentUser);
                                    taskReformResultMapper.updateByQuery(updateReform, QueryWrapper.create()
                                            .eq(TaskReformResultEntity::getHistoryId, reviewSaveDto.getHistoryId()));
                                    taskHistoryMapper.update(history, true);
                                    taskListMapper.update(task, true);
                                } else {
                                    // 给下个节点发送复核任务
                                    var nextUser = projectMemberMap.get(reviewSaveDto.getRoleCode().equals(EnumRole.DEVELOPMENT_MANAGER.getCode()) ? EnumRole.DELIVERY_REVIEW.getCode() : reviewSaveDto.getRoleCode().equals(EnumRole.DELIVERY_REVIEW.getCode()) ? EnumRole.ENGINEERING_LEADER.getCode() : EnumRole.DELIVERY_LEADER.getCode());
                                    if (Objects.nonNull(nextUser)) {
                                        var reformList = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                                                .eq(TaskReformResultEntity::getHistoryId, reviewSaveDto.getHistoryId()));
                                        // 任务
                                        for (var item : reformList) {
                                            var subMessage = messageService.createTaskBuilder(true)
                                                    .title("竣工验收复核任务")
                                                    .content(String.format("您有一个项目%s-%s的验收整改复核任务，请及时处理", history.getStoreCode(), history.getStoreName()))
                                                    .businessId(item.getId())
                                                    .businessName("竣工验收")
                                                    .moduleId(EnumModule.Review.getIndex())
                                                    .moduleName(EnumModule.Review.getName())
                                                    .url(String.format("/audit-mobile-app/audit/reform/%s", item.getId()))
                                                    .projectMember(nextUser)
                                                    .createUser(currentUser)
                                                    .parentBusinessId(item.getHistoryId())
                                                    .id(IdUtils.nextSnowFlakeId())
                                                    .build();
                                            messageList.add(subMessage);
                                        }
                                    }
                                }
                            }
                        }
                        // 新增新的任务
                        if (!messageList.isEmpty()) {
                            messageService.addMessage(messageList);
                        }
                        return true;
                    });
                    return RestUtils.success("成功");
                });
    }

    /**
     * 保存日志
     *
     * @param list        列表
     * @param currentUser 前用户
     * @param type        类型 1-验收，2-整改，3-复核
     */
    private void saveLog(List<TaskCheckLogEntity> list, CurrentUser currentUser, Integer type) {
        // 从当前用户的角色中匹配传入的roleId,获取对应的角色名称
        // 获取任务
        var taskId = list.get(0).getTaskId();
        var task = taskListMapper.selectOneById(taskId);
        if (Objects.nonNull(task)) {
            // 获取表单
            var form = formMapper.selectOneById(task.getFormId());
            if (Objects.nonNull(form)) {
                var roleId = type.equals(1) ? form.getCheckRoles() : (type.equals(2) ? form.getReformRoles() : form.getReviewRoles());
                var roleIdList = Arrays.stream(roleId.split(",")).toList();
                list.forEach(s -> {
                    // 从当前用户角色中匹配并获取角色名称
                    List<String> matchedRoleNames = currentUser.getRoles().stream()
                            .filter(role -> roleIdList.contains(role.getId()))
                            .map(RoleEntity::getCnName)
                            .toList();
                    if (!CollectionUtils.isEmpty(matchedRoleNames)) {
                        s.setRoleName(matchedRoleNames.get(0));
                    } else {
                        s.setRoleName(currentUser.getRoles().get(0).getCnName());
                    }
                    s.setTime(LocalDateTime.now());
                    s.setUserAccount(currentUser.getAccount());
                    s.setUserId(currentUser.getUserId());
                    s.setUserName(currentUser.getUserName());
                    s.setUserMobile(currentUser.getMobile());
                    s.buildCreateInfo(currentUser);
                });
                taskCheckLogMapper.insertBatch(list, 200);
            }
        }
    }

    /**
     * 判断当前用户是否都复核完成了
     *
     * @param historyId 历史ID
     * @param userId    用户ID
     * @return 结果
     */
    private boolean hasAllReviewAccepted(String historyId, String userId) {
        var allCount = taskReformResultMapper.selectCountByQuery(QueryWrapper.create()
                .eq(TaskReformResultEntity::getHistoryId, historyId));
        var count = taskReformResultMapper.selectCountByQuery(QueryWrapper.create()
                .eq(TaskReformResultEntity::getHistoryId, historyId)
                .eq(TaskReformResultEntity::getReviewUserId, userId)
                .eq(TaskReformResultEntity::getReviewResult, EnumReviewResult.Accept.getIndex()));
        return allCount == count;
    }

    /**
     * 获取验收记录
     *
     * @param checkId 验收ID
     * @return 记录
     */
    public List<TaskCheckLogEntity> getCheckLog(String checkId) {
        return taskCheckLogMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskCheckLogEntity::getCheckId, checkId));
    }

    /**
     * 获取用户列表
     *
     * @param body 查询条件
     * @return 结果
     */
    public List<UserEntity> getUserList(FormUserQueryDto body) {
        var form = formMapper.selectOneById(body.getFormId());
        if (Objects.nonNull(form)) {
            var roleIds = body.getType() == 1 ? form.getReformRoles() : form.getReviewRoles();
            return userService.getUserListByRole(Arrays.asList(roleIds.split(",")), body.getUserName());
        }
        return null;
    }

    /**
     * 获取验收用户信息
     *
     * @param body 查询条件
     * @return 结果
     */
    public Mono<List<UserEntity>> getCheckUserList(TaskListQueryDto body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var query = createQueryWrapper(body, currentUser)
                            .select("check_user_id, check_user_name, check_user_nick_name");
                    List<TaskListEntity> list = taskListMapper.selectListByQuery(query);
                    var userList = list.stream()
                            .collect(Collectors.toMap(
                                    TaskListEntity::getCheckUserId,
                                    item -> {
                                        var user = new UserEntity();
                                        user.setId(item.getCheckUserId());
                                        user.setCnName(item.getCheckUserName());
                                        return user;
                                    },
                                    (existing, replacement) -> existing
                            ))
                            .values()
                            .stream()
                            .toList();
                    return Mono.just(userList);
                });
    }

    /**
     * 开始验收
     *
     * @param body 请求
     * @return 结果
     */
    public Mono<Response<TaskHistoryEntity>> startCheck(TaskHistoryEntity body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    body.buildCreateInfo(currentUser);
                    var task = taskListMapper.selectOneById(body.getTaskId());
                    if (Objects.isNull(task)) {
                        return RestUtils.throwError("该验收任务不存在");
                    }
                    // 判断上一次的历史是否验收通过或者整改通过
                    if (body.getCheckNum() > 1) {
                        var lastHistory = taskHistoryMapper.selectOneByQuery(QueryWrapper.create()
                                .eq(TaskHistoryEntity::getTaskId, body.getTaskId())
                                .eq(TaskHistoryEntity::getCheckNum, body.getCheckNum() - 1));
                        if (Objects.isNull(lastHistory)) {
                            return RestUtils.throwError("上一次验收不存在");
                        }
                        // 如果上次验收还未结束，则直接返回上次验收的ID
                        if (lastHistory.getStatus() != EnumTaskStatus.ACCEPTED.getIndex() && lastHistory.getStatus() != EnumTaskStatus.REFORM_APPROVED.getIndex()) {
                            return RestUtils.success(lastHistory);
                        }
                    }
                    task.setCheckTime(LocalDateTime.now());
                    task.setCheckTimes(body.getCheckNum());
                    task.setCheckUserId(currentUser.getUserId());
                    task.setCheckUserMobile(currentUser.getMobile());
                    task.setCheckUserName(currentUser.getUserName());
                    task.setCheckUserNickName(currentUser.getUserName());
                    task.setStatus(EnumTaskStatus.PENDING_ACCEPTANCE.getIndex());
                    task.buildModifiedInfo(currentUser);

                    var form = formMapper.selectOneById(task.getFormId());
                    if (Objects.isNull(form)) {
                        return RestUtils.throwError("未获取到表单信息或者表单已失效或者表单未发布，请检查表单信息");
                    }
                    // 判断验收历史是否存在，如果存在，则直接返回，否则新增
                    var history = taskHistoryMapper.selectOneByQuery(QueryWrapper.create()
                            .eq(TaskHistoryEntity::getTaskId, task.getId())
                            .eq(TaskHistoryEntity::getCheckNum, body.getCheckNum()));
                    if (Objects.nonNull(history)) {
                        return RestUtils.success(history);
                    }

                    // 设置历史状态等
                    body.setProjectId(task.getProjectId());
                    body.setStatus(EnumTaskStatus.PENDING_ACCEPTANCE.getIndex());
                    body.setCheckTime(LocalDateTime.now());
                    body.setCheckUserId(currentUser.getUserId());
                    body.setCheckUserMobile(currentUser.getMobile());
                    body.setCheckUserName(currentUser.getUserName());
                    body.setCheckUserNickName(currentUser.getUserName());
                    body.buildCreateInfo(currentUser);
                    body.setFormId(task.getFormId());
                    body.setStoreName(task.getProjectName());
                    body.setStoreCode(task.getProjectNo());

                    // 获取表单分类树
                    var tree = formService.getCategoryTree(task.getFormId());
                    if (Objects.nonNull(tree)) {
                        body.setCategoryTree((JSONArray) JSON.toJSON(tree));
                    }
                    // 初始化必采项
                    var dicEntity = new DirectoryEntity();
                    dicEntity.setTypeId(EnumDirectoryType.PROFESSIONAL_SUB_CONTRACT_TYPE.getIndex());
                    dicEntity.setComment("必采");
                    var mustPurchaseList = directoryService.getList(dicEntity);
                    if (!mustPurchaseList.isEmpty()) {
                        var jsonList = new JSONArray();
                        for (var item : mustPurchaseList) {
                            var json = new JSONObject();
                            json.put("name", item.getEnName());
                            json.put("displayName", item.getName());
                            json.put("id", item.getEnumNo());
                            json.put("value", 1);
                            jsonList.add(json);
                        }
                        body.setMustPurchaseInfo(jsonList);
                    }

                    // 获取表单所有分类信息
                    var categoryMap = formCategoryMapper.selectListByQuery(QueryWrapper.create().eq(FormCategoryEntity::getFormId, task.getFormId()))
                            .stream()
                            .collect(Collectors.toMap(FormCategoryEntity::getId, category -> category));
                    // 获取所有问题
                    var questionList = taskListMapper.selectListByQueryAs(QueryWrapper.create()
                            .select("b.*, c.is_room_check, a.category_id, a.order_no, c.name as category_name, c.level as category_level, c.parent_id as category_parent_id")
                            .from(FormCategoryQuestionEntity.class).as("a")
                            .innerJoin(QuestionEntity.class).as("b").on(FormCategoryQuestionEntity::getQuestionId, QuestionEntity::getId)
                            .innerJoin(FormCategoryEntity.class).as("c").on(FormCategoryQuestionEntity::getCategoryId, FormCategoryEntity::getId)
                            .eq(FormCategoryQuestionEntity::getFormId, task.getFormId())
                            .orderBy(FormCategoryQuestionEntity::getOrderNo, true), FormQuestionDto.class);
                    var checkList = new ArrayList<TaskCheckResultEntity>();
                    for (var question : questionList) {
                        var taskCheck = new TaskCheckResultEntity();
                        taskCheck.setCategoryId(question.getCategoryId());
                        taskCheck.setQuestionId(question.getId());
                        taskCheck.setQuestionClassifyId(question.getClassifyId());
                        taskCheck.setQuestionClassifyName(question.getClassifyName());
                        taskCheck.setQuestionFileId(question.getFileId());
                        taskCheck.setTaskId(task.getId());
                        taskCheck.setCheckNum(body.getCheckNum());
                        taskCheck.setCategoryName(question.getCategoryName());
                        taskCheck.setQuestionCommentRequired(question.getCommentRequired());
                        taskCheck.setQuestionName(question.getName());
                        taskCheck.setQuestionOptions(question.getOptions());
                        taskCheck.setQuestionPlaceholder(question.getPlaceholder());
                        taskCheck.setQuestionIsDeductScore(question.getIsDeductScore());
                        taskCheck.setQuestionScore(question.getScore());
                        taskCheck.setQuestionPhotoRequired(question.getPhotoRequired());
                        taskCheck.setQuestionType(question.getType());
                        taskCheck.setQuestionPropsFields(question.getPropsFields());
                        taskCheck.setQuestionRiskLevel(question.getRiskLevel());
                        taskCheck.setQuestionSearchKeyWords(question.getSearchKeyWords());
                        taskCheck.setQuestionRequired(question.getIsRequired());
                        taskCheck.setIsRoomCheck(question.getIsRoomCheck());
                        taskCheck.setIsNoiseCheck(false);
                        if (Objects.equals(1, question.getCategoryLevel())) {
                            // 一级分类
                            taskCheck.setFirstCategoryId(taskCheck.getCategoryId());
                            taskCheck.setFirstCategoryName(taskCheck.getCategoryName());
                        }
                        if (Objects.equals(2, question.getCategoryLevel())) {
                            // 一级分类
                            var firstCategory = categoryMap.get(question.getCategoryParentId());
                            if (Objects.nonNull(firstCategory)) {
                                taskCheck.setFirstCategoryId(firstCategory.getId());
                                taskCheck.setFirstCategoryName(firstCategory.getName());
                            }
                        }
                        if (Objects.equals(3, question.getCategoryLevel())) {
                            // 二级分类
                            var secondCategory = categoryMap.get(question.getCategoryParentId());
                            if (Objects.nonNull(secondCategory)) {
                                taskCheck.setSecondCategoryId(secondCategory.getId());
                                taskCheck.setSecondCategoryName(secondCategory.getName());
                                // 一级分类
                                var firstCategory = categoryMap.get(secondCategory.getParentId());
                                if (Objects.nonNull(firstCategory)) {
                                    taskCheck.setFirstCategoryId(firstCategory.getId());
                                    taskCheck.setFirstCategoryName(firstCategory.getName());
                                }
                                // 设置是否按房间验收
                                taskCheck.setIsRoomCheck(secondCategory.getIsRoomCheck());
                            }
                        }
                        // 默认值，设置验收结果
                        var json = taskCheck.getQuestionOptions();
                        if (json != null) {
                            for (Object item : json) {
                                if (item instanceof JSONObject jsonObj &&
                                        jsonObj.containsKey("default") &&
                                        jsonObj.getIntValue("default") == 1) {
                                    JSONObject option = jsonObj.getJSONObject("option");
                                    if (option != null) {
                                        taskCheck.setCheckResult(option.getInteger("enumNo"));
                                        taskCheck.setCheckResultName(jsonObj.getString("name"));
                                    }
                                    break;
                                }
                            }
                        }
                        // 设置填写项的默认值
                        var propsJson = taskCheck.getQuestionPropsFields();
                        var result = new JSONArray();
                        boolean isMustPurchase = false;
                        if (propsJson != null) {
                            for (Object item : propsJson) {
                                if (item instanceof JSONObject jsonObj) {
                                    var obj = new JSONObject();
                                    // 如果选择的专业分包类型是必采项，则需要设置对应的是否必采项和是否平台采购
                                    if ("categoryPackageTypeId".equals(jsonObj.getString("name")) && jsonObj.containsKey("default")) {
                                        isMustPurchase = mustPurchaseList.stream().anyMatch(s -> s.getEnumNo().equals(jsonObj.getInteger("default")));
                                    }
                                    obj.put("id", jsonObj.getString("id"));
                                    obj.put("name", jsonObj.getString("name"));
                                    if ("isMustPurchase".equals(jsonObj.getString("name")) || "isPlatformPurchase".equals(jsonObj.getString("name"))) {
                                        obj.put("value", isMustPurchase ? 1 : 0);
                                    } else {
                                        obj.put("value", jsonObj.containsKey("default") ? jsonObj.getString("default") : "");
                                    }
                                    obj.put("displayName", jsonObj.getString("displayName"));
                                    result.add(obj);
                                    // 设置是否噪音专项
                                    if (jsonObj.getString("name").equals("isNoise") && jsonObj.getInteger("func").equals(1)) {
                                        taskCheck.setIsNoiseCheck(true);
                                    }
                                }
                            }
                            taskCheck.setResultJson(result);
                        }

                        taskCheck.buildCreateInfo(currentUser);
                        checkList.add(taskCheck);
                    }
                    // 初始化项目信息
                    var projectJson = new JSONArray();
                    var jsonObject = new JSONObject();
                    jsonObject.put("name", "hotelBuilding");
                    jsonObject.put("displayName", "酒店建筑");
                    jsonObject.put("value", "");
                    projectJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "riskManagement");
                    jsonObject.put("displayName", "风险管理");
                    jsonObject.put("value", "");
                    projectJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "facilityManagement");
                    jsonObject.put("displayName", "设施管理");
                    jsonObject.put("value", "");
                    projectJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "propertyDivision");
                    jsonObject.put("displayName", "物业划分");
                    jsonObject.put("value", "");
                    projectJson.add(jsonObject);

                    body.setProjectInfo(projectJson);
                    var userJson = new JSONArray();
                    jsonObject = new JSONObject();
                    jsonObject.put("name", "network");
                    jsonObject.put("displayName", "网络");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "soundproof");
                    jsonObject.put("displayName", "隔音");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "smell");
                    jsonObject.put("displayName", "异味");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "power");
                    jsonObject.put("displayName", "空调");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "water");
                    jsonObject.put("displayName", "热水");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    jsonObject = new JSONObject();
                    jsonObject.put("name", "other");
                    jsonObject.put("displayName", "其他");
                    jsonObject.put("value", "");
                    userJson.add(jsonObject);

                    body.setUserExperienceInfo(userJson);

                    // 查询代办任务，完成任务
                    var message = messageService.getBusinessTask(task.getId(), currentUser.getUserId(), EnumModule.Check.getIndex());

                    Db.tx(() -> {
                        taskListMapper.update(task, true);
                        taskHistoryMapper.insert(body);
                        checkList.forEach(check -> check.setHistoryId(body.getId()));
                        taskCheckResultMapper.insertBatch(checkList, 500);
                        if (Objects.nonNull(message)) {
                            messageService.finishMessage(message.getId(), currentUser, false);
                        }
                        return true;
                    });
                    return RestUtils.success(body);
                });
    }

    /**
     * 获取验收任务历史列表
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public List<TaskHistoryEntity> getTaskHistoryList(String taskId) {
        return taskHistoryMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskHistoryEntity::getTaskId, taskId)
                .orderBy(TaskHistoryEntity::getCheckNum, true));
    }

    /**
     * 保存验收任务历史
     *
     * @param body 任务历史
     * @return 结果
     */
    public Mono<Response<String>> saveTaskHistory(TaskHistoryEntity body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    body.buildModifiedInfo(currentUser);
                    var history = taskHistoryMapper.selectOneById(body.getId());
                    if (Objects.isNull(history)) {
                        return RestUtils.throwError("该验收不存在");
                    }
                    // 保存验收历史
                    taskHistoryMapper.update(body, true);
                    return RestUtils.success("保存成功");
                });
    }

    /**
     * 获取房间列表
     *
     * @param body body
     * @return 结果
     */
    public List<RoomEntity> getRoomList(RoomQueryDto body) {
        // 获取项目中的申请验收房间号
        var project = acmsProjectService.getProjectInfo(body.getProjectId());
        var node = acmsProjectService.getProjectNode(project.getProjectId(), project.getNodeTableName(), EnumProjectNode.CHECK_ROOM_LIST);
        // 获取项目下的所有房间
        var query = QueryWrapper.create()
                .eq(RoomEntity::getProjectId, body.getProjectId());
        if (StringUtils.hasLength(node.getNodeValue())) {
            query.in(RoomEntity::getRoomNum, Arrays.stream(node.getNodeValue().split(",")).toList());
        }
        // 按照楼层和房间号排序
        query.orderBy(RoomEntity::getFloorNum, true).orderBy(RoomEntity::getRoomNum, true);
        List<RoomEntity> roomList = roomMapper.selectListByQuery(query);
        // 查询已检查的房间
        var checkRoomList = taskCheckRoomMapper.selectListByQuery(QueryWrapper.create()
                        .eq(TaskCheckRoomEntity::getHistoryId, body.getHistoryId())
                        .eq(TaskCheckRoomEntity::getCategoryId, body.getCategoryId()))
                .stream()
                .collect(Collectors.groupingBy(TaskCheckRoomEntity::getRoomNum));
        for (var room : roomList) {
            if (checkRoomList.containsKey(room.getRoomNum())) {
                room.setStatus(checkRoomList.get(room.getRoomNum()).get(0).getStatus());
            } else {
                room.setStatus(EnumRoomStatus.NOT_STARTED.getIndex());
            }
        }
        return roomList;
    }

    /**
     * 获取分类下的房间
     *
     * @param body 请求
     * @return 结果
     */
    public List<TaskCheckRoomEntity> getCategoryRoomList(TaskCheckRoomEntity body) {
        return taskCheckRoomMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskCheckRoomEntity::getHistoryId, body.getHistoryId())
                .eq(TaskCheckRoomEntity::getCategoryId, body.getCategoryId())
                .orderBy(TaskCheckRoomEntity::getRoomNum, true));
    }

    /**
     * 获取房间下的问题列表
     *
     * @param body 请求
     * @return 结果
     */
    public List<TaskCheckResultRoomEntity> getCheckRoomQuestionList(TaskCheckResultRoomEntity body) {
        var query = QueryWrapper.create()
                .eq(TaskCheckResultRoomEntity::getHistoryId, body.getHistoryId())
                .eq(TaskCheckResultRoomEntity::getCategoryId, body.getCategoryId())
                .eq(TaskCheckResultRoomEntity::getRoomNum, body.getRoomNum());
        // 如果是噪音专项，则过滤是噪音的问题
        if (Objects.nonNull(body.getIsNoiseCheck()) && body.getIsNoiseCheck()) {
            query.eq(TaskCheckResultRoomEntity::getIsNoiseCheck, true);
        }
        return taskCheckResultRoomMapper.selectListByQuery(query);
    }

    /**
     * 新增房间 问题列表
     *
     * @param dto 请求体
     */
    public Mono<Response<String>> addCheckRoom(TaskCheckRoomAddDto dto) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    // 获取二级分类及下所有三级分类的问题
                    var questionList = taskCheckResultMapper.selectListByQuery(QueryWrapper.create()
                            .eq(TaskCheckResultEntity::getHistoryId, dto.getHistoryId())
                            .and(QueryMethods.column(TaskCheckResultEntity::getCategoryId).eq(dto.getCategoryId())
                                    .or(QueryMethods.column(TaskCheckResultEntity::getSecondCategoryId).eq(dto.getCategoryId()))));
                    if (questionList.isEmpty()) {
                        return RestUtils.throwError("未找到分类下的问题");
                    }
                    // 遍历房间，构造问题列表
                    var roomQuestionList = new ArrayList<TaskCheckResultRoomEntity>();
                    var checkRoomList = new ArrayList<TaskCheckRoomEntity>();
                    for (var room : dto.getRoomList()) {
                        // 构造房间
                        var checkRoom = new TaskCheckRoomEntity();
                        checkRoom.setHistoryId(dto.getHistoryId());
                        checkRoom.setCategoryId(dto.getCategoryId());
                        checkRoom.setRoomNum(room.getRoomNum());
                        checkRoom.setFloorNum(room.getFloorNum());
                        checkRoom.setStatus(EnumRoomStatus.PROCESSING.getIndex());
                        checkRoom.buildCreateInfo(currentUser);
                        for (var i = 0; i < questionList.size(); i++) {
                            var question = questionList.get(i);
                            checkRoom.setTaskId(question.getTaskId());
                            checkRoom.setCheckNum(question.getCheckNum());
                            checkRoom.setCategoryName(StringUtils.hasLength(question.getSecondCategoryId()) ? question.getSecondCategoryName() : question.getCategoryName());
                            // 构造问题
                            var roomQuestion = new TaskCheckResultRoomEntity();
                            // 排除fileId, checkResult,
                            BeanUtils.copyProperties(question, roomQuestion);
                            roomQuestion.setRoomNum(room.getRoomNum());
                            roomQuestion.setFloorNum(room.getFloorNum());
                            // 如果是噪音专项，需要设置验收结果
                            if (Objects.nonNull(dto.getIsNoiseCheck()) && dto.getIsNoiseCheck()
                                    && StringUtils.hasLength(question.getSecondCategoryId())
                                    && question.getSecondCategoryId().equals(dto.getCategoryId())) {
                                roomQuestion.setCheckResult(EnumCheckResult.UNQUALIFIED.getIndex());
                                roomQuestion.setCheckResultName(EnumCheckResult.UNQUALIFIED.getName());
                                roomQuestion.setFileId(i == 0 ? dto.getFileId() : ossService.copy(dto.getFileId()));
                                roomQuestion.setComment(dto.getComment());
                            }

                            roomQuestion.setId(null);
                            roomQuestion.setCheckId(question.getId());
                            roomQuestion.buildCreateInfo(currentUser);
                            roomQuestion.buildModifiedInfo(currentUser);
                            roomQuestionList.add(roomQuestion);
                        }
                        checkRoomList.add(checkRoom);
                    }
                    Db.tx(() -> {
                        taskCheckRoomMapper.insertBatch(checkRoomList, 100);
                        taskCheckResultRoomMapper.insertBatch(roomQuestionList, 100);
                        return true;
                    });
                    return RestUtils.success("成功");
                });
    }

    /**
     * 提交验收历史
     *
     * @param historyId 历史ID
     * @return 结果
     */
    public Mono<Response<TaskSubmitResDto>> submitCheckHistory(String historyId) {
        return userService.getCurrentUser()
                .flatMap(currentUser -> {
                    var history = taskHistoryMapper.selectOneById(historyId);
                    if (Objects.isNull(history) || history.getStatus() != EnumTaskStatus.PENDING_ACCEPTANCE.getIndex()) {
                        return RestUtils.throwError("未找到该验收历史或者验收历史状态不是待验收，请检查");
                    }
                    var projectInfo = history.getProjectInfo();
                    if (projectInfo.isEmpty()) {
                        return RestUtils.throwError("未填写项目情况，请检查");
                    }
                    for (var item : projectInfo) {
                        var obj = (JSONObject) item;
                        if (obj.getString("value").isEmpty()) {
                            return RestUtils.throwError("项目情况未填写完整，请检查");
                        }
                    }
                    var experienceInfo = history.getUserExperienceInfo();
                    if (experienceInfo.isEmpty()) {
                        return RestUtils.throwError("未填写用户体验情况，请检查");
                    }
                    for (var item : experienceInfo) {
                        var obj = (JSONObject) item;
                        if (!Objects.equals(obj.getString("name"), "other") && obj.getString("value").isEmpty()) {
                            return RestUtils.throwError("用户体验情况未填写完整，请检查");
                        }
                    }

                    // 验收问题校验
                    var questionList = taskCheckResultMapper.selectListByQuery(QueryWrapper.create()
                            .eq(TaskCheckResultEntity::getHistoryId, historyId));
                    // 房间检查结果
                    var roomCheckList = taskCheckResultRoomMapper.selectListByQuery(QueryWrapper.create()
                            .eq(TaskCheckResultRoomEntity::getHistoryId, historyId));
                    var acmsProject = acmsProjectService.getProjectInfo(history.getProjectId());
                    var roomNode = acmsProjectService.getProjectNode(acmsProject.getProjectId(), acmsProject.getNodeTableName(), EnumProjectNode.CHECK_ROOM_LIST);
                    // 获取所有的房间(如果有验收申请房间号，则取，否则取所有）
                    var roomList = Objects.nonNull(roomNode) && StringUtils.hasLength(roomNode.getNodeValue()) ?
                            roomMapper.selectListByQuery(QueryWrapper.create()
                                    .eq(RoomEntity::getProjectId, history.getProjectId())
                                    .in(RoomEntity::getRoomNum, Arrays.stream(roomNode.getNodeValue().split(",")).toList()))
                            : roomMapper.selectListByQuery(QueryWrapper.create().eq(RoomEntity::getProjectId, history.getProjectId()));
                    // 校验房间规则
                    validateRoom(roomList, roomCheckList);
                    // 验收问题(非完工照片， 非按房间检查）
                    if (Objects.nonNull(questionList)) {
                        var oneOptional = questionList.stream().filter(item -> !item.getIsRoomCheck() && item.getQuestionType() != EnumQuestionType.File.getIndex()
                                && (Objects.isNull(item.getCheckResult()) || (item.getCheckResult().equals(EnumCheckResult.UNQUALIFIED.getIndex()) && (!StringUtils.hasLength(item.getComment()) || !StringUtils.hasLength(item.getFileId()))))).findFirst();
                        if (oneOptional.isPresent()) {
                            var question = oneOptional.get();
                            // 返回有问题的问题，便于前端定位到对应的问题
                            return RestUtils.success(TaskSubmitResDto.build(question.getFirstCategoryId(), question.getSecondCategoryId(), question.getCategoryId(), "", question.getId(), false,
                                    String.format("%s/%s/%s的问题[%s]未填写，请检查", question.getFirstCategoryName(), question.getSecondCategoryName(), question.getCategoryName(), question.getQuestionName())));
                        }
                    }
                    // 完工照片校验(非按房间检查的项）
                    var finishPhotoOptional = questionList
                            .stream()
                            .filter(q -> !q.getIsRoomCheck() && q.getQuestionType() == EnumQuestionType.File.getIndex() && !StringUtils.hasLength(q.getFileId()))
                            .findFirst();
                    if (finishPhotoOptional.isPresent()) {
                        var finishPhoto = finishPhotoOptional.get();
                        return RestUtils.success(TaskSubmitResDto.build(finishPhoto.getFirstCategoryId(), finishPhoto.getSecondCategoryId(), finishPhoto.getCategoryId(), "", finishPhoto.getId(), true,
                                String.format("%s/%s的完工照片[%s]未上传，请检查", finishPhoto.getFirstCategoryName(), finishPhoto.getCategoryName(), finishPhoto.getQuestionName())));
                    }

                    // 如果是按房间检查，但对应的房间检查没有记录，则提示要选择房间检查
                    if (!questionList.isEmpty()) {
                        var oneOptional = questionList.stream().filter(item -> item.getIsRoomCheck() && Objects.isNull(roomCheckList.stream().filter(r -> r.getCheckId().equals(item.getId())).findFirst().orElse(null))).findFirst();
                        if (oneOptional.isPresent()) {
                            var question = oneOptional.get();
                            return RestUtils.success(TaskSubmitResDto.build(question.getFirstCategoryId(), question.getSecondCategoryId(), question.getCategoryId(),
                                    "", question.getId(), question.getQuestionType() == EnumQuestionType.File.getIndex(),
                                    String.format("%s/%s/%s的%s[%s]未选择房间检查，请检查", question.getFirstCategoryName(), StringUtils.hasLength(question.getSecondCategoryName()) ? question.getSecondCategoryName() : ""
                                            , question.getCategoryName(),
                                            question.getQuestionType() == EnumQuestionType.File.getIndex() ? "完工照片" : "问题",
                                            question.getQuestionName())));
                        }
                    }
                    // 查找房间非完工照片，是否都填写了检查结果
                    if (!roomCheckList.isEmpty()) {
                        var roomCheckOptional = roomCheckList.stream().filter(item -> item.getQuestionType() != EnumQuestionType.File.getIndex()
                                && (Objects.isNull(item.getCheckResult()) || (item.getCheckResult().equals(EnumCheckResult.UNQUALIFIED.getIndex()) && (!StringUtils.hasLength(item.getComment()) || !StringUtils.hasLength(item.getFileId()))))).findFirst();
                        if (roomCheckOptional.isPresent()) {
                            var question = roomCheckOptional.get();
                            var check = questionList.stream().filter(q -> q.getId().equals(question.getCheckId())).findFirst().orElse(new TaskCheckResultEntity());
                            return RestUtils.success(TaskSubmitResDto.build(check.getFirstCategoryId(), check.getSecondCategoryId(), check.getCategoryId(), question.getRoomNum(), question.getId(), false,
                                    String.format("%s房间%s的问题[%s]未填写，请检查", question.getCategoryName(), question.getRoomNum(), question.getQuestionName())));
                        }
                    }

                    // 查找房间完工照片，是否都上传了
                    if (!roomCheckList.isEmpty()) {
                        var roomCheckOptional = roomCheckList.stream().filter(item -> item.getQuestionType() == EnumQuestionType.File.getIndex() && !StringUtils.hasLength(item.getFileId())).findFirst();
                        if (roomCheckOptional.isPresent()) {
                            var question = roomCheckOptional.get();
                            var check = questionList.stream().filter(q -> q.getId().equals(question.getCheckId())).findFirst().orElse(new TaskCheckResultEntity());
                            return RestUtils.success(TaskSubmitResDto.build(check.getFirstCategoryId(), check.getSecondCategoryId(), check.getCategoryId(), question.getRoomNum(), question.getId(), true,
                                    String.format("%s的房间%s的完工照片%s未上传，请检查", question.getCategoryName(), question.getRoomNum(), question.getQuestionName())));
                        }
                    }

                    var task = taskListMapper.selectOneById(history.getTaskId());
                    if (Objects.isNull(task)) {
                        return RestUtils.throwError("未找到该验收任务，请检查");
                    }

                    // 获取房间检查项，设置
                    var roomResultList = roomCheckList.stream()
                            .collect(Collectors.groupingBy(TaskCheckResultRoomEntity::getCheckId));
                    var reformList = new ArrayList<TaskReformResultEntity>();
                    var checkUpdateList = new ArrayList<TaskCheckResultEntity>();
                    // 获取存在的整改数据
                    var existsReformMap = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                                    .eq(TaskReformResultEntity::getHistoryId, historyId))
                            .stream()
                            .collect(Collectors.toMap(TaskReformResultEntity::getCheckId, Function.identity()));
                    // 需要更新的整改数据
                    var updateReformList = new ArrayList<TaskReformResultEntity>();
                    // 需要删除的整改数据
                    var deleteReformIds = new ArrayList<String>();
                    var messageList = new ArrayList<MessageEntity>();
                    var checkUser = projectService.getMemberByQuery(history.getProjectId(), EnumRole.DELIVERY_ACCEPTANCE);

                    if (!questionList.isEmpty()) {
                        for (var check : questionList) {
                            // 如果是完工照片，则跳过
                            if (check.getQuestionType() == EnumQuestionType.File.getIndex()) {
                                continue;
                            }
                            var newCheck = new TaskCheckResultEntity();
                            var checkRoomList = roomResultList.get(check.getId());
                            // 按照房间综合汇总结果，对整改结果更新
                            if (check.getIsRoomCheck() && Objects.nonNull(checkRoomList)) {
                                // 5间房间，以上不合格，则为不合格
                                if (checkRoomList.stream().filter(r -> r.getCheckResult().equals(EnumCheckResult.UNQUALIFIED.getIndex())).count() >= 5) {
                                    newCheck.setCheckResult(EnumCheckResult.UNQUALIFIED.getIndex());
                                    newCheck.setCheckResultName(EnumCheckResult.UNQUALIFIED.getName());
                                    newCheck.setComment("根据房间累计5次不合格，判定为不合格");
                                    // 每个房间对应的属性值都是一致的，所以取第一个即可
                                    newCheck.setResultJson(checkRoomList.get(0).getResultJson());
                                    // 赋值，用于后面的判断
                                    check.setCheckResult(EnumCheckResult.UNQUALIFIED.getIndex());
                                    check.setCheckResultName(EnumCheckResult.UNQUALIFIED.getName());
                                } else {
                                    newCheck.setCheckResult(EnumCheckResult.QUALIFIED.getIndex());
                                    newCheck.setCheckResultName(EnumCheckResult.QUALIFIED.getName());
                                    // 赋值，用于后面的判断
                                    check.setCheckResult(EnumCheckResult.QUALIFIED.getIndex());
                                    check.setCheckResultName(EnumCheckResult.QUALIFIED.getName());
                                }
                                newCheck.setId(check.getId());
                                newCheck.buildModifiedInfo(currentUser);
                                checkUpdateList.add(newCheck);
                            }
                            // 如果存在的整改项中现在判定是合格的，需要从整改项中删除
                            if (check.getCheckResult() == EnumCheckResult.QUALIFIED.getIndex()
                                    && !check.getIsRoomCheck()
                                    && check.getQuestionType() != EnumQuestionType.File.getIndex()
                                    && existsReformMap.containsKey(check.getId())) {
                                deleteReformIds.add(existsReformMap.get(check.getId()).getId());
                            }

                            // 整改项的判断，非房间检查的项
                            if (check.getCheckResult() == EnumCheckResult.UNQUALIFIED.getIndex() && !check.getIsRoomCheck() && check.getQuestionType() != EnumQuestionType.File.getIndex()) {
                                var reform = new TaskReformResultEntity();
                                if (existsReformMap.containsKey(check.getId())) {
                                    var originReform = existsReformMap.get(check.getId());
                                    reform.setId(originReform.getId());
                                    reform.setStatus(EnumReformStatus.Scheme.getIndex());
                                    reform.buildModifiedInfo(currentUser);
                                    updateReformList.add(reform);
                                } else {
                                    BeanUtils.copyProperties(check, reform);
                                    reform.setCheckId(check.getId());
                                    reform.setId(IdUtils.nextSnowFlakeId());
                                    reform.setIsRoomCheck(false);
                                    reform.setStatus(EnumReformStatus.Scheme.getIndex());
                                    reform.setFloorNum(0);
                                    reform.setRoomNum("");
                                    reform.buildCreateInfo(currentUser);
                                    reform.buildModifiedInfo(currentUser);
                                    reformList.add(reform);
                                }
                                // 给验收人员发送任务
                                var message = messageService.createTaskBuilder(true)
                                        .title("竣工验收整改方案/计划整改日期任务")
                                        .content(String.format("您有一个项目%s-%s的整改方案/计划整改日期填写任务请及时处理", task.getProjectNo(), task.getProjectName()))
                                        .businessId(reform.getId())
                                        .businessName("竣工验收")
                                        .moduleId(EnumModule.ReformScheme.getIndex())
                                        .moduleName(EnumModule.ReformScheme.getName())
                                        .url("/audit-mobile-app/audit/reform/" + reform.getId())
                                        .projectMember(checkUser)
                                        .createUser(currentUser)
                                        .build();
                                messageList.add(message);
                            }
                        }

                        // 获取房间的不合格项（排除完工照片）
                        var unqualifiedRoomList = roomCheckList.stream().filter(r -> r.getQuestionType() == EnumQuestionType.SingleSelect.getIndex() && r.getCheckResult().equals(EnumCheckResult.UNQUALIFIED.getIndex())).toList();
                        var checkMap = questionList.stream().collect(Collectors.toMap(TaskCheckResultEntity::getId, Function.identity()));
                        for (var checkRoom : unqualifiedRoomList) {
                            var reform = new TaskReformResultEntity();
                            if (existsReformMap.containsKey(checkRoom.getId())) {
                                var originReform = existsReformMap.get(checkRoom.getId());
                                reform.setId(originReform.getId());
                                reform.setStatus(EnumReformStatus.Scheme.getIndex());
                                reform.buildModifiedInfo(currentUser);
                                updateReformList.add(reform);
                            } else {
                                BeanUtils.copyProperties(checkRoom, reform);
                                var check = checkMap.get(checkRoom.getCheckId());
                                reform.setCheckId(checkRoom.getId());
                                reform.setFirstCategoryId(check.getFirstCategoryId());
                                reform.setFirstCategoryName(check.getFirstCategoryName());
                                reform.setSecondCategoryId(check.getSecondCategoryId());
                                reform.setSecondCategoryName(check.getSecondCategoryName());
                                reform.setId(IdUtils.nextSnowFlakeId());
                                reform.setCheckNum(check.getCheckNum());
                                reform.setRoomNum(checkRoom.getRoomNum());
                                reform.setFloorNum(checkRoom.getFloorNum());
                                reform.setIsRoomCheck(true);
                                reform.setStatus(EnumReformStatus.Scheme.getIndex());
                                reform.buildCreateInfo(currentUser);
                                reform.buildModifiedInfo(currentUser);
                                reformList.add(reform);
                            }
                            // 给验收人员发送任务
                            var message = messageService.createTaskBuilder(true)
                                    .title("竣工验收整改方案/计划整改日期任务")
                                    .content(String.format("您有一个项目%s-%s的整改方案/计划整改日期填写任务请及时处理", task.getProjectNo(), task.getProjectName()))
                                    .businessId(reform.getId())
                                    .businessName("竣工验收")
                                    .moduleId(EnumModule.ReformScheme.getIndex())
                                    .moduleName(EnumModule.ReformScheme.getName())
                                    .url("/audit-mobile-app/audit/reform/" + reform.getId())
                                    .projectMember(checkUser)
                                    .createUser(currentUser)
                                    .build();
                            messageList.add(message);
                        }

                        // 获取房间的合格项，从已经有的整改记录中删除
                        var qualifiedRoomList = roomCheckList.stream().filter(r -> r.getQuestionType() == EnumQuestionType.SingleSelect.getIndex()
                                && r.getCheckResult().equals(EnumCheckResult.QUALIFIED.getIndex())
                                && existsReformMap.containsKey(r.getId())).toList();
                        if (!qualifiedRoomList.isEmpty()) {
                            for (var checkRoom : qualifiedRoomList) {
                                var reform = existsReformMap.get(checkRoom.getId());
                                deleteReformIds.add(reform.getId());
                            }
                        }
                    }

                    var hasReform = reformList.size() > 0 || updateReformList.size() > 0;

                    // 更新验收任务状态为填写整改方案
                    task.setStatus(hasReform ? EnumTaskStatus.IN_ACCEPTANCE.getIndex() : EnumTaskStatus.IN_RECEIPT.getIndex());
                    task.setCheckTime(LocalDateTime.now());
                    task.setCheckUserId(currentUser.getUserId());
                    task.setCheckUserMobile(currentUser.getMobile());
                    task.setCheckUserName(currentUser.getUserName());
                    task.setCheckUserNickName(currentUser.getUserName());
                    task.buildModifiedInfo(currentUser);
                    // 更新验收历史状态
                    history.setStatus(hasReform ? EnumTaskStatus.IN_ACCEPTANCE.getIndex() : EnumTaskStatus.IN_RECEIPT.getIndex());
                    history.setCheckTime(LocalDateTime.now());
                    history.setCheckUserId(currentUser.getUserId());
                    history.setCheckUserMobile(currentUser.getMobile());
                    history.setCheckUserName(currentUser.getUserName());
                    history.setCheckUserNickName(currentUser.getUserName());
                    history.buildModifiedInfo(currentUser);
                    TaskHistoryReceiptEntity receipt;
                    if (!hasReform) {
                        // 生成回执单
                        receipt = buildReceipt(history, currentUser);
                        // 给验收人员发送回执单任务
                        var message = messageService.createTaskBuilder(true)
                                .title("竣工验收回执单提交任务")
                                .content(String.format("您有一个项目%s-%s的回执单提交任务，请及时处理", task.getProjectNo(), task.getProjectName()))
                                .businessId(history.getId())
                                .businessName("竣工验收")
                                .moduleId(EnumModule.Receipt.getIndex())
                                .moduleName(EnumModule.Receipt.getName())
                                .url("/audit-mobile-app/audit/detail/" + history.getId() + "?tab=4")
                                .projectMember(checkUser)
                                .createUser(currentUser)
                                .build();
                        messageList.add(message);
                    } else {
                        receipt = null;
                    }

                    Db.tx(() -> {
                        taskListMapper.update(task, true);
                        taskHistoryMapper.update(history, true);
                        if (!checkUpdateList.isEmpty()) {
                            Db.executeBatch(checkUpdateList, 100, TaskCheckResultMapper.class, (mapper, entity) -> mapper.update(entity, true));
                        }
                        if (!reformList.isEmpty()) {
                            taskReformResultMapper.insertBatch(reformList, 100);
                        }
                        if (!updateReformList.isEmpty()) {
                            Db.executeBatch(updateReformList, 100, TaskReformResultMapper.class, (mapper, entity) -> mapper.update(entity, true));
                        }
                        if (!deleteReformIds.isEmpty()) {
                            taskReformResultMapper.deleteBatchByIds(deleteReformIds);
                        }
                        if (Objects.nonNull(receipt)) {
                            taskHistoryReceiptMapper.insertOrUpdate(receipt);
                        }
                        if (!messageList.isEmpty()) {
                            messageService.addMessage(messageList);
                        }
                        return true;
                    });
                    return RestUtils.success(new TaskSubmitResDto());
                });
    }

    /**
     * 校验房间
     *
     * @param roomList      房间列表
     * @param roomCheckList 房间检查列表
     */
    private void validateRoom(List<RoomEntity> roomList, List<TaskCheckResultRoomEntity> roomCheckList) {
        //验收房间数是否大于15%或不低于15间的验收数量，覆盖所有楼层（每层至少2间，各楼层房间尾号不重复）
        // 1. 计算验收房间比例
        long checkedRoomCount = roomCheckList.stream()
                .map(TaskCheckResultRoomEntity::getRoomNum)
                .distinct()
                .count();
        // 2. 检查验收房间数量是否满足15%且不低于15间
        if (checkedRoomCount < Math.max(roomCheckMinNums, Math.floor(roomList.size() * roomCheckRatio))) {
            throw new CommonException(String.format(
                    "验收房间数量不足，需验收至少%.0f间(总房间数的%.0f%%)或不少于%d间，当前验收%d间",
                    Math.floor(roomList.size() * roomCheckRatio), roomCheckRatio * 100, roomCheckMinNums, checkedRoomCount
            ));
        }

        // 3. 检查楼层覆盖情况（每层至少2间）
        Map<Integer, Set<String>> floorRoomMap = roomCheckList.stream()
                .collect(Collectors.groupingBy(
                        TaskCheckResultRoomEntity::getFloorNum,
                        Collectors.mapping(
                                TaskCheckResultRoomEntity::getRoomNum,
                                Collectors.toSet()
                        )
                ));
        List<String> violations = new ArrayList<>();
        // 检查每个楼层的去重后房间数量
        var index = new AtomicInteger(1);
        for (Map.Entry<Integer, Set<String>> entry : floorRoomMap.entrySet()) {
            int floor = entry.getKey();
            int distinctRoomCount = entry.getValue().size();
            if (distinctRoomCount < 2) {
                violations.add(String.format(
                        "%d.楼层%s验收房间不足，每层至少需要验收2间，当前验收%d间",
                        index.getAndIncrement(), floor, distinctRoomCount
                ));
            }
        }
        if (!violations.isEmpty()) {
            throw new CommonException(String.join("<br/>", violations));
        }

        // 4. 检查房间尾号不重复
        // 4.1 按照房间号去重
        Map<String, TaskCheckResultRoomEntity> uniqueRooms = roomCheckList.stream()
                .collect(Collectors.toMap(
                        TaskCheckResultRoomEntity::getRoomNum,
                        room -> room,
                        (existing, replacement) -> existing
                ));
        // 4.2 提取尾号并检查重复
        Map<String, List<TaskCheckResultRoomEntity>> tailNumberMap = new HashMap<>();
        for (TaskCheckResultRoomEntity room : uniqueRooms.values()) {
            String roomNum = room.getRoomNum();
            String tailNumber = roomNum.substring(Math.max(0, roomNum.length() - 2));
            tailNumberMap.computeIfAbsent(tailNumber, k -> new ArrayList<>()).add(room);
        }
        // 4.3 检查是否有重复尾号
        List<String> duplicateMessages = new ArrayList<>();
        Set<String> allTailNumbers = roomList.stream().map(r -> r.getRoomNum().substring(Math.max(0, r.getRoomNum().length() - 2))).collect(Collectors.toSet());
        boolean hasDuplicates = false;

        for (Map.Entry<String, List<TaskCheckResultRoomEntity>> entry : tailNumberMap.entrySet()) {
            List<TaskCheckResultRoomEntity> sameTailRooms = entry.getValue();
            if (sameTailRooms.size() > 1) {
                hasDuplicates = true;
                String roomsInfo = sameTailRooms.stream()
                        .map(r -> r.getFloorNum() + "-" + r.getRoomNum())
                        .collect(Collectors.joining(", "));
                duplicateMessages.add("[" + roomsInfo + "]");
            }
        }

        if (hasDuplicates) {
            // 检查是否覆盖了所有指定的尾号
            Set<String> actualTailNumbers = tailNumberMap.keySet();
            if (!actualTailNumbers.containsAll(allTailNumbers)){
                Set<String> missing = new HashSet<>(allTailNumbers);
                missing.removeAll(actualTailNumbers);
                throw new CommonException("房间尾号重复，重复的房间有" + String.join("，", duplicateMessages) + "，缺失尾号: " + missing);
            }
        }
    }

    /**
     * 获取回执单
     *
     * @param historyId 任务历史ID
     * @return 结果
     */
    public TaskHistoryReceiptEntity getTaskHistoryReceipt(String historyId) {
        return taskHistoryReceiptMapper.selectOneByQuery(QueryWrapper.create()
                .eq(TaskHistoryReceiptEntity::getHistoryId, historyId));
    }

    /**
     * 保存回执单
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<String>> saveTaskHistoryReceipt(TaskHistoryReceiptEntity body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var receipt = taskHistoryReceiptMapper.selectOneById(body.getId());
                    if (Objects.isNull(receipt)) {
                        return RestUtils.throwError("未找到该回执单，请检查");
                    }
                    if (receipt.getStatus() == EnumReceiptStatus.SUBMITTED.getIndex()) {
                        return RestUtils.throwError("该回执单已提交，无法修改");
                    }
                    if (body.getStatus() == EnumReceiptStatus.SUBMITTED.getIndex()) {
                        // 生成评分表
                        var history = taskHistoryMapper.selectOneById(receipt.getHistoryId());
                        if (Objects.isNull(history) || history.getStatus() != EnumTaskStatus.IN_RECEIPT.getIndex()) {
                            return RestUtils.throwError("未找到该验收历史或者验收历史状态不是回执单，请检查");
                        }
                        // 获取验收问题，排除完工照片和自定义的问题
                        var checkList = taskCheckResultMapper.selectListByQuery(QueryWrapper.create()
                                .eq(TaskCheckResultEntity::getHistoryId, receipt.getHistoryId())
                                .ne(TaskCheckResultEntity::getQuestionClassifyId, EnumQuestionClassify.OtherItem.getIndex())
                                .eq(TaskCheckResultEntity::getQuestionType, EnumQuestionType.SingleSelect.getIndex()));
                        if (checkList.isEmpty()) {
                            return RestUtils.throwError("该验收任务没有验收问题，请检查");
                        }
                        var partsMap = directoryService.getList(EnumDirectoryType.PART.getIndex()).stream()
                                .collect(Collectors.toMap(DirectoryEntity::getEnumNo, DirectoryEntity::getName));
                        var categoryPackageTypeMap = directoryService.getList(EnumDirectoryType.PROFESSIONAL_SUB_CONTRACT_TYPE.getIndex()).stream()
                                .collect(Collectors.toMap(DirectoryEntity::getEnumNo, DirectoryEntity::getName));
                        var scoreList = new ArrayList<TaskScoreEntity>();
                        for (var check : checkList) {
                            var score = new TaskScoreEntity();
                            score.setTaskId(history.getTaskId());
                            score.setHistoryId(receipt.getHistoryId());
                            score.setFirstCategoryId(check.getFirstCategoryId());
                            score.setFirstCategoryName(check.getFirstCategoryName());
                            score.setSecondCategoryId(check.getSecondCategoryId());
                            score.setSecondCategoryName(check.getSecondCategoryName());
                            score.setThirdCategoryId(check.getCategoryId());
                            score.setThirdCategoryName(check.getCategoryName());
                            score.setQuestionId(check.getQuestionId());
                            score.setQuestionId(check.getQuestionId());
                            score.setQuestionName(check.getQuestionName());
                            score.setQuestionTypeId(check.getQuestionClassifyId());
                            score.setQuestionTypeName(check.getQuestionClassifyName());
                            score.setFullScore(check.getQuestionScore());
                            // 设置分部
                            var partOptional = check.getQuestionPropsFields().stream().filter(json -> {
                                var obj = (JSONObject) json;
                                return obj.getString("name").equals("part");
                            }).map(json -> {
                                var obj = (JSONObject) json;
                                return obj.getIntValue("default");
                            }).findFirst();
                            if (partOptional.isPresent()) {
                                score.setPartId(partOptional.get());
                                score.setPartName(StringUtils.hasLength(partsMap.get(partOptional.get())) ? partsMap.get(partOptional.get()) : "");
                            } else {
                                score.setPartId(0);
                                score.setPartName("");
                            }
                            score.setIsQualified(check.getCheckResult() == EnumCheckResult.QUALIFIED.getIndex());

                            // 获取result ,转换为jsonArray
                            var resultJson = check.getResultJson();
                            if (Objects.nonNull(resultJson)) {
                                for (var item : resultJson) {
                                    var jsonObject = (JSONObject) item;
                                    if (jsonObject.containsKey("name") && "categoryPackageTypeId".equals(jsonObject.getString("name"))) {
                                        score.setCategoryPackageTypeId(jsonObject.getInteger("value"));
                                        score.setCategoryPackageTypeName(categoryPackageTypeMap.get(jsonObject.getInteger("value")));
                                    }
                                    if (jsonObject.containsKey("name") && "isTotalPackageRegion".equals(jsonObject.getString("name"))) {
                                        score.setIsTotalPackageRegion(jsonObject.getInteger("value").equals(1));
                                    }
                                }
                            }
                            // 设置分数
                            if (score.getIsQualified()) {
                                score.setProductScore(score.getFullScore());
                            } else {
                                score.setProductScore(BigDecimal.valueOf(0));
                            }
                            score.buildCreateInfo(currentUser);
                            scoreList.add(score);
                        }
                        // 产品得分逻辑
                        //  - 当验收项验收时选择【合格】、获取验收项配置的满分分值
                        //  - 若问题分类为“视觉项”、“设施项”的验收项选择【不合格】时，该项不得分
                        //  - 若问题分类为“功能区清零”的验收项选择【不合格】时，该验收项所属功能区得分为0
                        //  - 若问题分类为“分部清零”的验收项选择【不合格】时，该验收项所属功能区的分部得分为0
                        //  - 若问题分类为“一票否决项”的验收项选择【不合格】时，分数还是给的，只是加个整体不合格的标识
                        // 查找所有分部清零项
                        var partClearList = scoreList.stream().filter(score -> !score.getIsQualified() && score.getQuestionTypeId() == EnumQuestionClassify.PartClear.getIndex()).toList();
                        if (!partClearList.isEmpty()) {
                            for (var score : partClearList) {
                                // 获取当前问题对应的二级下的相同分部的问题，分数清零
                                var detailList = scoreList.stream().filter(s -> s.getSecondCategoryId().equals(score.getSecondCategoryId()) && Objects.equals(s.getPartId(), score.getPartId())).toList();
                                detailList.forEach(detail -> detail.setProductScore(BigDecimal.valueOf(0)));
                            }
                        }
                        // 查找功能区清零项
                        var functionAreaClearList = scoreList.stream().filter(score -> !score.getIsQualified() && score.getQuestionTypeId() == EnumQuestionClassify.FunctionAreaClear.getIndex()).toList();
                        if (!functionAreaClearList.isEmpty()) {
                            for (var score : functionAreaClearList) {
                                // 获取当前问题对应的所有二级，分数清零
                                var detailList = scoreList.stream().filter(s -> s.getSecondCategoryId().equals(score.getSecondCategoryId())).toList();
                                detailList.forEach(detail -> detail.setProductScore(BigDecimal.valueOf(0)));
                            }
                        }
                        // 查找一票否决项
                        if (scoreList.stream().anyMatch(score -> !score.getIsQualified() && score.getQuestionTypeId() == EnumQuestionClassify.NoneItem.getIndex())) {
                            // 分数不扣除，但整体不合格
                            history.setCheckResult(EnumCheckResult.UNQUALIFIED.getIndex());
                        }

                        // 设置状态为审批中
                        history.setStatus(EnumTaskStatus.IN_APPROVAL.getIndex());
                        history.buildModifiedInfo(currentUser);
                        var task = taskListMapper.selectOneById(history.getTaskId());
                        task.setStatus(EnumTaskStatus.IN_APPROVAL.getIndex());
                        // 设置评分
                        history.setCheckScore(scoreList.stream().map(TaskScoreEntity::getProductScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                        history.setCheckFullScore(scoreList.stream().map(TaskScoreEntity::getFullScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                        task.setCheckScore(history.getCheckScore());
                        task.setCheckFullScore(history.getCheckFullScore());
                        task.buildModifiedInfo(currentUser);
                        body.buildModifiedInfo(currentUser);
                        // 获取项目的交付支持中心工程负责人，插入任务
                        var member = projectService.getMemberByQuery(task.getProjectId(), EnumRole.ENGINEERING_LEADER);
                        MessageEntity messageEntity;
                        var approveList = new ArrayList<ApproveLogEntity>();
                        if (Objects.nonNull(member)) {
                            messageEntity = messageService.createTaskBuilder(true)
                                    .title("竣工验收评分表审批任务")
                                    .content(String.format("您有一个项目%s-%s的评分表审批任务，请及时处理", history.getStoreCode(), history.getStoreName()))
                                    .url(String.format("/audit-mobile-app/audit/detail/%s?tab=5", history.getId()))
                                    .businessId(history.getId())
                                    .businessName("竣工验收")
                                    .moduleId(EnumModule.Score.getIndex())
                                    .moduleName(EnumModule.Score.getName())
                                    .projectMember(member)
                                    .createUser(currentUser)
                                    .build();
                            var approveLog = messageService.createApproveLogBuilder()
                                    .businessId(history.getId())
                                    .businessName("评分表")
                                    .action("已提交")
                                    .user(currentUser)
                                    .roleCode(EnumRole.DELIVERY_ACCEPTANCE.getCode())
                                    .build();
                            approveList.add(approveLog);
                        } else {
                            messageEntity = null;
                        }
                        // 查询代办任务，完成任务
                        var message = messageService.getBusinessTask(history.getId(), currentUser.getUserId(), EnumModule.Receipt.getIndex());
                        // 更新数据库
                        Db.tx(() -> {
                            taskHistoryReceiptMapper.update(body, true);
                            taskHistoryMapper.update(history, true);
                            taskListMapper.update(task, true);
                            if (scoreList.size() > 0) {
                                // 先删除评分
                                taskScoreMapper.deleteByQuery(QueryWrapper.create()
                                        .eq(TaskScoreEntity::getHistoryId, receipt.getHistoryId()));
                                taskScoreMapper.insertBatch(scoreList, 100);
                            }
                            if (Objects.nonNull(messageEntity)) {
                                messageService.addMessage(messageEntity);
                            }
                            if (approveList.size() > 0) {
                                messageService.addApproveLog(approveList);
                            }
                            if (Objects.nonNull(message)) {
                                messageService.finishMessage(message.getId(), currentUser, false);
                            }
                            return true;
                        });

                    } else {
                        body.buildModifiedInfo(currentUser);
                        taskHistoryReceiptMapper.update(body, true);
                    }
                    return RestUtils.success("成功");
                });
    }

    /**
     * 获取历史评分列表
     *
     * @param historyId 历史ID
     * @return 结果
     */
    public TaskScoreDto getTaskHistoryScore(String historyId) {
        var taskScoreDto = new TaskScoreDto();
        var list = taskScoreMapper.selectListByQuery(QueryWrapper.create()
                .eq(TaskScoreEntity::getHistoryId, historyId));
        var result = new ArrayList<TaskScoreTreeDto>();
        // 按一级分类分组
        var map = list.stream().collect(Collectors.groupingBy(TaskScoreEntity::getFirstCategoryId));
        for (var item : map.entrySet()) {
            var first = new TaskScoreTreeDto();
            first.setId(item.getKey());
            first.setName(item.getValue().get(0).getFirstCategoryName());
            // 汇总分数
            first.setFullScore(item.getValue().stream().map(TaskScoreEntity::getFullScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            first.setProductScore(item.getValue().stream().map(TaskScoreEntity::getProductScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 按二级分类分组
            var secondMap = item.getValue().stream().collect(Collectors.groupingBy(TaskScoreEntity::getSecondCategoryId));
            var secondList = new ArrayList<TaskScoreTreeDto>();
            for (var secondItem : secondMap.entrySet()) {
                var second = new TaskScoreTreeDto();
                second.setId(secondItem.getKey());
                second.setName(secondItem.getValue().get(0).getSecondCategoryName());
                // 汇总分数
                second.setFullScore(secondItem.getValue().stream().map(TaskScoreEntity::getFullScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                second.setProductScore(secondItem.getValue().stream().map(TaskScoreEntity::getProductScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                secondList.add(second);
            }
            first.setChildren(secondList);
            result.add(first);
        }
        taskScoreDto.setAreaScores(result);

        // 获取总包及专业分包的分数
        result = new ArrayList<>();
        // 总包分数
        var totalPackageScore = new TaskScoreTreeDto();
        totalPackageScore.setId("totalPackageScore");
        totalPackageScore.setName("总包");
        totalPackageScore.setFullScore(list.stream().filter(TaskScoreEntity::getIsTotalPackageRegion).map(TaskScoreEntity::getFullScore).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalPackageScore.setProductScore(list.stream().filter(TaskScoreEntity::getIsTotalPackageRegion).map(TaskScoreEntity::getProductScore).reduce(BigDecimal.ZERO, BigDecimal::add));
        result.add(totalPackageScore);
        // 专业分包分数, 按照专业分包类型分组
        var packageMap = list.stream().filter(s -> Objects.nonNull(s.getCategoryPackageTypeId()) && EnumCategoryPackageType.Total.getIndex() != s.getCategoryPackageTypeId()).collect(Collectors.groupingBy(TaskScoreEntity::getCategoryPackageTypeId));
        for (var item : packageMap.entrySet()) {
            var packageScore = new TaskScoreTreeDto();
            packageScore.setId(item.getKey().toString());
            packageScore.setName(item.getValue().get(0).getCategoryPackageTypeName());
            packageScore.setFullScore(item.getValue().stream().map(TaskScoreEntity::getFullScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            packageScore.setProductScore(item.getValue().stream().map(TaskScoreEntity::getProductScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            result.add(packageScore);
        }
        taskScoreDto.setPackageScores(result);
        // 必采项
        var history = taskHistoryMapper.selectOneById(historyId);
        if (Objects.nonNull(history)) {
            var mustItems = history.getMustPurchaseInfo();
            for (var item : mustItems) {
                var json = (JSONObject) item;
                var packages = packageMap.get(json.getInteger("id"));
                if (Objects.nonNull(packages) && packages.size() > 0) {
                    var isQualified = packages.stream().allMatch(TaskScoreEntity::getIsQualified);
                    json.put("isQualified", isQualified);
                }
            }
            taskScoreDto.setMustItems(mustItems);
        }
        return taskScoreDto;
    }

    /**
     * 获取不合格问题
     *
     * @param historyId 历史ID
     * @return 结果
     */
    public List<TaskReformResultDto> getUnQualifiedCheckQuestion(String historyId) {
        return taskReformResultMapper.selectListByQueryAs(QueryWrapper.create()
                .select(""" 
                        t_audit_task_reform_result.*,
                        IF(t_audit_task_check_result.file_id is null, t_audit_task_check_result_room.file_id,
                                  t_audit_task_check_result.file_id) as check_file_id,
                               IF(t_audit_task_check_result.comment is null, t_audit_task_check_result_room.comment,
                                  t_audit_task_check_result.comment) as check_comment
                        """)
                .from(TaskReformResultEntity.class)
                .leftJoin(TaskCheckResultEntity.class).on(TaskReformResultEntity::getCheckId, TaskCheckResultEntity::getId)
                .leftJoin(TaskCheckResultRoomEntity.class).on(TaskReformResultEntity::getCheckId, TaskCheckResultRoomEntity::getId)
                .eq(TaskReformResultEntity::getHistoryId, historyId), TaskReformResultDto.class);
    }

    /**
     * 删除房间
     *
     * @param body 请求
     * @return 结果
     */
    public Integer delCheckRoom(TaskCheckRoomAddDto body) {
        // 删除检查房间和对应的问题
        AtomicInteger result = new AtomicInteger();
        Db.tx(() -> {
            // 删除房间对应的问题，可能包含三级下的
            result.addAndGet(taskCheckResultRoomMapper.deleteByQuery(QueryWrapper.create()
                    .from(TaskCheckResultRoomEntity.class)
                    .innerJoin(TaskCheckRoomEntity.class).on("t_audit_task_check_result_room.task_id = t_audit_task_check_room.task_id and t_audit_task_check_result_room.history_id = t_audit_task_check_room.history_id and t_audit_task_check_result_room.room_num = t_audit_task_check_room.room_num")
                    .eq(TaskCheckRoomEntity::getHistoryId, body.getHistoryId())
                    .eq(TaskCheckRoomEntity::getCategoryId, body.getCategoryId())
                    .in(TaskCheckRoomEntity::getRoomNum, body.getRoomList().stream().map(RoomEntity::getRoomNum).toList())));
            // 删除检查房间
            result.addAndGet(taskCheckRoomMapper.deleteByQuery(QueryWrapper.create()
                    .eq(TaskCheckRoomEntity::getHistoryId, body.getHistoryId())
                    .eq(TaskCheckRoomEntity::getCategoryId, body.getCategoryId())
                    .in(TaskCheckRoomEntity::getRoomNum, body.getRoomList().stream().map(RoomEntity::getRoomNum).toList())));
            return true;
        });
        return result.get();
    }

    /**
     * 生成回执单
     *
     * @param historyId 历史ID
     * @return 结果
     */
    public Mono<Response<String>> addTaskHistoryReceipt(String historyId) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var history = taskHistoryMapper.selectOneById(historyId);
                    if (Objects.isNull(history)) {
                        return RestUtils.throwError("未找到该验收历史，请检查");
                    }
                    if (history.getStatus() != EnumTaskStatus.IN_ACCEPTANCE.getIndex()) {
                        return RestUtils.throwError("该验收历史状态不是整改方案，请检查");
                    }
                    var task = taskListMapper.selectOneById(history.getTaskId());
                    if (Objects.isNull(task)) {
                        return RestUtils.throwError("未找到该验收任务，请检查");
                    }
                    // 判断整改方案和预计整改完成时间是否都已填写
                    var list = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                            .eq(TaskReformResultEntity::getHistoryId, historyId)
                            .and(QueryMethods.column(TaskReformResultEntity::getReformPlanScheme).isNull()
                                    .or(QueryMethods.column(TaskReformResultEntity::getReformPlanFinishTime).isNull())));
                    if (Objects.nonNull(list) && !list.isEmpty()) {
                        // 生成全部的信息，格式按照如下
                        var counter = new AtomicInteger(1);
                        return RestUtils.throwError(list.stream().map(one -> String.format("%d.%s%s%s%s%s还未填写整改方案和计划整改完成日期", counter.getAndIncrement(), one.getFirstCategoryName()
                                , one.getSecondCategoryName(), one.getCategoryName(), one.getRoomNum(), one.getQuestionName())).collect(Collectors.joining("<br/>")));
                    }

                    // 设置Task和History的状态为回执单
                    task.setStatus(EnumTaskStatus.IN_RECEIPT.getIndex());
                    task.buildModifiedInfo(currentUser);
                    history.setStatus(EnumTaskStatus.IN_RECEIPT.getIndex());
                    history.buildModifiedInfo(currentUser);
                    // 回执单
                    var receipt = buildReceipt(history, currentUser);
                    var checkUser = projectService.getMemberByQuery(task.getProjectId(), EnumRole.DELIVERY_ACCEPTANCE);
                    // 给验收人员发送回执单任务
                    var message = messageService.createTaskBuilder(true)
                            .title("竣工验收回执单提交任务")
                            .content(String.format("您有一个项目%s-%s的回执单提交任务，请及时处理", task.getProjectNo(), task.getProjectName()))
                            .businessId(history.getId())
                            .businessName("竣工验收")
                            .moduleId(EnumModule.Receipt.getIndex())
                            .moduleName(EnumModule.Receipt.getName())
                            .url("/audit-mobile-app/audit/detail/" + history.getId() + "?tab=4")
                            .projectMember(checkUser)
                            .createUser(currentUser)
                            .build();

                    Db.tx(() -> {
                        taskHistoryReceiptMapper.insertOrUpdate(receipt, true);
                        taskListMapper.update(task, true);
                        taskHistoryMapper.update(history, true);
                        messageService.addMessage(message);
                        return true;
                    });

                    return RestUtils.success("成功");
                });
    }

    /**
     * 评分表审批
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<String>> approveScore(ScoreApproveDto body) {
        var history = taskHistoryMapper.selectOneById(body.getHistoryId());
        if (Objects.isNull(history)) {
            return RestUtils.throwError("记录不存在");
        }
        if (history.getStatus() != EnumTaskStatus.IN_APPROVAL.getIndex()) {
            return RestUtils.throwError("记录状态不正确");
        }
        var task = taskListMapper.selectOneById(history.getTaskId());
        if (Objects.isNull(task)) {
            return RestUtils.throwError("任务不存在");
        }

        return UserUtils.get()
                .flatMap(currentUser -> {
                    // 同意，设置状态为待整改，拒绝设置状态为待验收
                    var messageList = new ArrayList<MessageEntity>();
                    if (body.getType() == 1) {
                        history.setStatus(EnumTaskStatus.PENDING_ACCEPTANCE.getIndex());
                        task.setStatus(EnumTaskStatus.PENDING_ACCEPTANCE.getIndex());
                        var checkUser = projectService.getMemberByQuery(history.getProjectId(), EnumRole.DELIVERY_ACCEPTANCE);
                        var messageEntity = messageService.createTaskBuilder(true)
                                .title("竣工验收任务")
                                .content(String.format("您有一个项目%s-%s的竣工验收被拒绝，请及时处理", history.getStoreCode(), history.getStoreName()))
                                .url(String.format("/audit-mobile-app/audit/detail/%s", history.getId()))
                                .businessId(history.getId())
                                .businessName("竣工验收")
                                .moduleId(EnumModule.Check.getIndex())
                                .moduleName(EnumModule.Check.getName())
                                .projectMember(checkUser)
                                .createUser(currentUser)
                                .id(IdUtils.nextSnowFlakeId())
                                .build();
                        messageList.add(messageEntity);
                    } else {
                        //判断是否需要整改,如果没有，则设置为验收完成
                        var list = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                                        .select("id")
                                        .eq(TaskReformResultEntity::getHistoryId, body.getHistoryId()))
                                .stream().map(TaskReformResultEntity::getId).toList();
                        if (list.size() > 0) {
                            history.setStatus(EnumTaskStatus.PENDING_REFORM.getIndex());
                            task.setStatus(EnumTaskStatus.PENDING_REFORM.getIndex());
                            // 发送任务给现长和项目经理
                            var projectMemberLeader = projectService.getMemberByQuery(task.getProjectId(), EnumRole.OPERATION_LEADER);
                            var projectMemberManager = projectService.getMemberByQuery(task.getProjectId(), EnumRole.DEVELOPMENT_MANAGER);
                            // 构造子任务
                            for (var id : list) {
                                var linkId = IdUtils.nextSnowFlakeId();
                                var messageEntity = messageService.createTaskBuilder(true)
                                        .title("竣工验收整改任务")
                                        .content(String.format("您有一个项目%s-%s的验收整改任务，请及时处理", history.getStoreCode(), history.getStoreName()))
                                        .url(String.format("/audit-mobile-app/audit/reform/%s", id))
                                        .businessId(id)
                                        .businessName("竣工验收")
                                        .moduleId(EnumModule.Reform.getIndex())
                                        .moduleName(EnumModule.Reform.getName())
                                        .projectMember(projectMemberLeader)
                                        .createUser(currentUser)
                                        .parentBusinessId(history.getId())
                                        // 关联ID
                                        .linkId(linkId)
                                        .id(IdUtils.nextSnowFlakeId())
                                        .build();
                                messageList.add(messageEntity);
                                messageEntity = messageService.createTaskBuilder(true)
                                        .title("竣工验收整改任务")
                                        .content(String.format("您有一个项目%s-%s的验收整改任务，请及时处理", history.getStoreCode(), history.getStoreName()))
                                        .url(String.format("/audit-mobile-app/audit/reform/%s", id))
                                        .businessId(id)
                                        .businessName("竣工验收")
                                        .moduleId(EnumModule.Reform.getIndex())
                                        .moduleName(EnumModule.Reform.getName())
                                        .projectMember(projectMemberManager)
                                        .createUser(currentUser)
                                        .parentBusinessId(history.getId())
                                        .id(linkId)
                                        .build();
                                messageList.add(messageEntity);
                            }
                        } else {
                            history.setStatus(EnumTaskStatus.ACCEPTED.getIndex());
                            task.setStatus(EnumTaskStatus.ACCEPTED.getIndex());
                        }
                    }
                    history.buildModifiedInfo(currentUser);
                    task.buildModifiedInfo(currentUser);
                    // 设置整改项的状态为待整改
                    var taskReformResult = new TaskReformResultEntity();
                    taskReformResult.setStatus(EnumReformStatus.Reforming.getIndex());
                    taskReformResult.buildModifiedInfo(currentUser);
                    // 记录审批日志
                    var approveLog = messageService.createApproveLogBuilder()
                            .businessId(history.getId())
                            .businessName("评分表")
                            .action(body.getType() == 1 ? "已退回" : "已通过")
                            .user(currentUser)
                            .roleCode(EnumRole.DELIVERY_LEADER.getCode())
                            .comment(body.getComment())
                            .build();

                    Db.tx(() -> {
                        taskHistoryMapper.update(history, true);
                        taskListMapper.update(task, true);
                        // 完成评分表任务
                        messageService.finishMessage(body.getMessageId(), currentUser, false);
                        // 审批记录
                        messageService.addApproveLog(approveLog);
                        // 设置整改项的状态为待整改
                        if (body.getType() == 2) {
                            taskReformResultMapper.updateByQuery(taskReformResult,
                                    QueryWrapper.create().eq(TaskReformResultEntity::getHistoryId, body.getHistoryId())
                            );
                        }
                        if (messageList.size() > 0) {
                            messageService.addMessage(messageList);
                        }
                        return true;
                    });
                    return RestUtils.success("成功");
                });
    }

    /**
     * 批量提交或退回整改
     *
     * @param body 集合
     * @return 结果
     */
    public Mono<Response<String>> submitOrBackReform(ReviewSaveDto body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var history = taskHistoryMapper.selectOneById(body.getHistoryId());
                    if (Objects.isNull(history)) {
                        return RestUtils.throwError("未找到验收历史，请检查");
                    }
                    var task = taskListMapper.selectOneById(history.getTaskId());
                    if (Objects.isNull(task)) {
                        return RestUtils.throwError("未找到验收任务，请检查");
                    }
                    var ids = body.getList().stream().map(ReviewSaveDto.ReviewItem::getId).toList();
                    var list = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                            .in(TaskReformResultEntity::getId, ids));
                    if (list.isEmpty()) {
                        return RestUtils.throwError("未找到整改项");
                    }
                    if (body.getType() == 2 && list.stream().anyMatch(s -> s.getStatus() != EnumReformStatus.Reforming.getIndex()
                            && s.getStatus() != EnumReformStatus.Commitment.getIndex()
                            && s.getStatus() != EnumReformStatus.Report.getIndex())) {
                        return RestUtils.throwError("只能提交待整改、承诺项、营建报备的数据，请检查");
                    }
                    if (body.getType() == 2 && list.stream().anyMatch(s -> Objects.isNull(s.getReformResult())
                            || !StringUtils.hasLength(s.getReformFileId()))) {
                        var optional = list.stream().filter(s -> Objects.isNull(s.getReformResult())
                                || !StringUtils.hasLength(s.getReformFileId())).findFirst();
                        if (optional.isPresent()) {
                            var one = optional.get();
                            return RestUtils.throwError(String.format("%s/%s/%s/%s的%s整改结果未填写，请检查", one.getFirstCategoryName(), one.getSecondCategoryName(), one.getCategoryName(), one.getRoomNum(), one.getQuestionName()));
                        }
                    }
                    if (body.getType() == 1 && list.stream().anyMatch(s -> s.getStatus() != EnumReformStatus.Commitment.getIndex()
                            && s.getStatus() != EnumReformStatus.Report.getIndex())) {
                        return RestUtils.throwError("只能退回承诺项、营建报备的数据，请检查");
                    }
                    var updateList = new ArrayList<TaskReformResultEntity>();
                    var approveList = new ArrayList<ApproveLogEntity>();
                    var messageList = new ArrayList<MessageEntity>();
                    var finishMessageIds = new HashSet<>(body.getList().stream().map(ReviewSaveDto.ReviewItem::getMessageId).toList());
                    for (var item : list) {
                        var update = new TaskReformResultEntity();
                        update.setId(item.getId());
                        update.setIsFileApproved(true);
                        update.buildModifiedInfo(currentUser);
                        // 提交时，设置状态为待复核
                        if (body.getType() == 2) {
                            update.setStatus(EnumReformStatus.Reviewing.getIndex());
                            update.setReformTime(LocalDateTime.now());
                            update.setReformUserId(currentUser.getUserId());
                            update.setReformUserName(currentUser.getUserName());
                            // 需要把所有的整改任务置为完成
                            var message = messageService.getBusinessTaskList(item.getId(), currentUser.getUserId(), EnumModule.Reform.getIndex());
                            if (!message.isEmpty()) {
                                finishMessageIds.addAll(message.stream().map(MessageEntity::getId).toList());
                            }
                        } else {
                            update.setStatus(EnumReformStatus.Reforming.getIndex());
                            // 给项目经理生成一条待整改的任务
                            var pm = projectService.getMemberByQuery(body.getProjectId(), EnumRole.DEVELOPMENT_MANAGER);
                            var messageEntity = messageService.createTaskBuilder(true)
                                    .title("竣工验收整改任务")
                                    .content(String.format("您有一个项目%s-%s的验收整改被退回，请及时处理", history.getStoreCode(), history.getStoreName()))
                                    .url(String.format("/audit-mobile-app/audit/reform/%s", item.getId()))
                                    .businessId(item.getId())
                                    .parentBusinessId(item.getHistoryId())
                                    .businessName("竣工验收")
                                    .moduleId(EnumModule.Reform.getIndex())
                                    .moduleName(EnumModule.Reform.getName())
                                    .projectMember(pm)
                                    .createUser(currentUser)
                                    .build();
                            messageList.add(messageEntity);
                        }
                        updateList.add(update);
                        // 审批记录
                        // 插入审批记录
                        var approveLog = messageService.createApproveLogBuilder()
                                .action(body.getType() == 2 ? item.getStatus() == EnumReformStatus.Reforming.getIndex() ? "已提交" : "资料通过" : "资料退回")
                                .user(currentUser)
                                .roleCode(EnumRole.OPERATION_LEADER.getCode())
                                .businessId(item.getId())
                                .businessName("验收整改")
                                .comment(body.getComment())
                                .build();
                        approveList.add(approveLog);
                    }

                    Db.tx(() -> {
                        if (updateList.size() > 0) {
                            Db.executeBatch(updateList, 100, TaskReformResultMapper.class, (mapper, entity) -> mapper.update(entity, true));
                        }
                        if (approveList.size() > 0) {
                            messageService.addApproveLog(approveList);
                        }
                        if (body.getType() == 2) {
                            // 判断整改项是否提交了,
                            var reformList = taskReformResultMapper.selectListByQuery(QueryWrapper.create()
                                    .eq(TaskReformResultEntity::getHistoryId, body.getHistoryId()));
                            // 判断是否都是待复核状态
                            if (reformList.stream().allMatch(s -> s.getStatus().equals(EnumReformStatus.Reviewing.getIndex()))) {
                                history.setStatus(EnumTaskStatus.Reviewing.getIndex());
                                history.buildModifiedInfo(currentUser);
                                task.setStatus(EnumTaskStatus.Reviewing.getIndex());
                                task.buildModifiedInfo(currentUser);
                                task.setReformTime(LocalDateTime.now());
                                task.setReformUserName(currentUser.getUserName());
                                task.setReformUserMobile(currentUser.getMobile());
                                task.setReformUserId(currentUser.getUserId());
                                task.setReformUserNickName(currentUser.getUserName());
                                // 发送项目经理
                                var projectMember = projectService.getMemberByQuery(task.getProjectId(), EnumRole.DEVELOPMENT_MANAGER);
                                // 构造子任务
                                for (var item : reformList) {
                                    var messageEntity = messageService.createTaskBuilder(true)
                                            .title("竣工验收整改复核任务")
                                            .content(String.format("您有一个项目%s-%s的验收整改复核任务，请及时处理", history.getStoreCode(), history.getStoreName()))
                                            .projectMember(projectMember)
                                            .url(String.format("/audit-mobile-app/audit/reform/%s", item.getId()))
                                            .businessId(item.getId())
                                            .businessName("竣工验收")
                                            .moduleId(EnumModule.Review.getIndex())
                                            .moduleName(EnumModule.Review.getName())
                                            .createUser(currentUser)
                                            .parentBusinessId(item.getHistoryId())
                                            .build();
                                    messageList.add(messageEntity);
                                }
                                taskHistoryMapper.update(history, true);
                                taskListMapper.update(task, true);
                            }
                        }
                        // 完成任务
                        if (!finishMessageIds.isEmpty()) {
                            messageService.finishMessage(finishMessageIds.stream().toList(), currentUser, false);
                        }
                        if (!messageList.isEmpty()) {
                            messageService.addMessage(messageList);
                        }
                        return true;
                    });
                    return RestUtils.success("成功");
                });
    }

    /**
     * 资料确认
     *
     * @param body 请求体
     * @return 结果
     */
    public Mono<Response<String>> reformFileConfirm(FileConfirmDto body) {
        return UserUtils.get()
                .flatMap(currentUser -> {
                    var reform = taskReformResultMapper.selectOneById(body.getId());
                    if (Objects.isNull(reform)) {
                        return RestUtils.throwError("未找到整改项，请检查");
                    }
                    if (reform.getStatus() != EnumReformStatus.Reforming.getIndex()) {
                        return RestUtils.throwError("该项已资料确认，请检查");
                    }
                    var update = new TaskReformResultEntity();
                    update.setId(reform.getId());
                    update.setStatus(reform.getReformResult() == EnumReformResult.Promise.getIndex() ? EnumReformStatus.Commitment.getIndex() : EnumReformStatus.Report.getIndex());
                    update.buildModifiedInfo(currentUser);
                    // 插入审批记录
                    var approveLog = messageService.createApproveLogBuilder()
                            .action("资料确认")
                            .user(currentUser)
                            .roleCode(EnumRole.DEVELOPMENT_MANAGER.getCode())
                            .businessId(body.getId())
                            .businessName("验收整改")
                            .build();
                    var messageList = new ArrayList<MessageEntity>();
                    if (Objects.nonNull(reform.getIsFileApproved()) && reform.getIsFileApproved()) {
                        // 生成任务给现长
                        var storeManager = projectService.getMemberByQuery(body.getProjectId(), EnumRole.OPERATION_LEADER);
                        var history = taskHistoryMapper.selectOneById(reform.getHistoryId());
                        var message = messageService.createTaskBuilder(true)
                                .title("竣工验收整改任务")
                                .content(String.format("您有一个项目%s-%s的验收整改任务审批，请及时处理", history.getStoreCode(), history.getStoreName()))
                                .url(String.format("/audit-mobile-app/audit/reform/%s", body.getId()))
                                .businessId(body.getId())
                                .businessName("竣工验收")
                                .moduleId(EnumModule.Reform.getIndex())
                                .moduleName(EnumModule.Reform.getName())
                                .projectMember(storeManager)
                                .createUser(currentUser)
                                .parentBusinessId(reform.getHistoryId())
                                .build();
                        messageList.add(message);
                    }
                    Db.tx(() -> {
                        messageService.addApproveLog(approveLog);
                        taskReformResultMapper.update(update, true);
                        // 完成任务
                        messageService.finishMessage(body.getMessageId(), currentUser, false);
                        if (messageList.size() > 0) {
                            messageService.addMessage(messageList);
                        }
                        return true;
                    });
                    return RestUtils.success("成功");
                });
    }

    /**
     * 根据任务ID和检查次数获取历史
     *
     * @param taskId   任务ID
     * @param checkNum 检查次数
     * @return 结果
     */
    public TaskHistoryEntity getTaskHistory(String taskId, String checkNum) {
        return taskHistoryMapper.selectOneByQuery(QueryWrapper.create()
                .eq(TaskHistoryEntity::getTaskId, taskId)
                .eq(TaskHistoryEntity::getCheckNum, checkNum));
    }

    /**
     * 构造回执单
     *
     * @param history     历史
     * @param currentUser 当前用户
     * @return TaskHistoryReceiptEntity
     */
    private TaskHistoryReceiptEntity buildReceipt(TaskHistoryEntity history, CurrentUser currentUser) {
        // 判断回执单是否存在，如果存在，则更新，否则生成回执单
        var receipt = new TaskHistoryReceiptEntity();
        var originReceipt = taskHistoryReceiptMapper.selectOneByQuery(QueryWrapper.create()
                .eq(TaskHistoryReceiptEntity::getHistoryId, history.getId()));
        receipt.setStatus(EnumReceiptStatus.NEW.getIndex());
        if (Objects.nonNull(originReceipt)) {
            receipt.setId(originReceipt.getId());
            receipt.buildModifiedInfo(currentUser);
        } else {
            receipt.setId(null);
            receipt.setTaskId(history.getTaskId());
            receipt.setHistoryId(history.getId());
            // 核心检查项
            var coreCheckItems = new JSONArray();
            // 查找验收问题中的核心检查项，从字典获取
            var items = directoryService.getList(EnumDirectoryType.CORE_CHECK.getIndex());

            if (!items.isEmpty()) {
                for (var check : items) {
                    var jsonObject = new JSONObject();
                    jsonObject.put("name", check.getEnName());
                    jsonObject.put("displayName", check.getName());
                    // 默认合格
                    jsonObject.put("result", EnumCheckResult.QUALIFIED.getIndex());
                    coreCheckItems.add(jsonObject);
                }
            }
            receipt.setCoreCheckItems(coreCheckItems);
            receipt.buildCreateInfo(currentUser);
            // 设置签名
            var sign = new JSONArray();
            var signObject = new JSONObject();
            signObject.put("name", "经营战区现长");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "经营战区门店维修工");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "开发战区营建项目经理");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "施工方人员");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "特许方人员");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "交付支持中心竣工验收人员");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            signObject = new JSONObject();
            signObject.put("name", "交付支持中心工程负责人");
            signObject.put("sign", "");
            signObject.put("time", "");
            sign.add(signObject);
            receipt.setSign(sign);
            receipt.setPropertyInfo(new JSONArray());
        }
        return receipt;
    }

    /**
     * 生成任务
     *
     * @param projectId   项目ID
     * @param currentUser 当前用户
     * @param formId      表单ID
     */
    public void generateTaskList(String projectId, CurrentUser currentUser, String formId) {
        var taskList = new TaskListEntity();
        taskList.buildCreateInfo(currentUser);
        var count = taskListMapper.selectCountByQuery(QueryWrapper.create()
                .eq(TaskListEntity::getProjectId, projectId));
        if (count > 0) {
            throw new CommonException("该项目已存在验收任务");
        }
        // 获取项目信息
        taskList.setStatus(EnumTaskStatus.PENDING_ACCEPTANCE.getIndex());
        var projectInfo = acmsProjectService.getProjectInfo(projectId);
        taskList.setProjectId(projectId);
        taskList.setProjectNo(projectInfo.getProjectNo());
        taskList.setProjectName(projectInfo.getProjectName());
        taskList.setProjectId(projectInfo.getProjectId());
        taskList.setStoreCode(Optional.ofNullable(projectInfo.getStoreNo()).orElse(""));
        taskList.setStoreName(Optional.ofNullable(projectInfo.getStoreName()).orElse(""));
        taskList.setAddress(Optional.ofNullable(projectInfo.getProjectAddress()).orElse(""));
        taskList.setBrandId(projectInfo.getBrandId().intValue());
        taskList.setBrandName(projectInfo.getBrandName());
        taskList.setMarketId(Integer.valueOf(projectInfo.getRegion()));
        taskList.setMarketName(projectInfo.getRegionName());
        taskList.setToAcmsCheckStatus(EnumSynStatus.UN_SYN.getIndex());
        taskList.setToAcmsReformStatus(EnumSynStatus.UN_SYN.getIndex());
        // 获取项目版本
        var dicProductVersionMap = directoryService.getList(EnumDirectoryType.PRODUCT_VERSION.getIndex()).stream().collect(Collectors.toMap(DirectoryEntity::getEnName, Function.identity()));
        var product = dicProductVersionMap.get(projectInfo.getProductCode());
        if (Objects.nonNull(product)) {
            taskList.setProductVersionId(product.getEnumNo());
            taskList.setProductVersion(projectInfo.getProductVersionName());
        }
        // 如果不传formId,则获取项目的模板
        var query = QueryWrapper.create();
        if (StringUtils.hasLength(formId)) {
            query.eq(FormEntity::getId, formId);
        } else {
            // 获取表单
            var formNode = acmsProjectService.getProjectNode(taskList.getProjectId(), projectInfo.getNodeTableName(), EnumProjectNode.CHECK_FORM);
            query.eq(FormEntity::getId, formNode.getNodeValue());
        }
        query.eq(FormEntity::getStatus, EnumFormStatus.Published.getIndex())
                .ge(FormEntity::getEndTime, LocalDateTime.now())
                .le(FormEntity::getStartTime, LocalDateTime.now())
                .orderBy(FormEntity::getCreateTime, false);
        var form = formMapper.selectOneByQuery(query);
        if (Objects.isNull(form)) {
            throw new CommonException("未获取到表单信息或者表单已失效或者表单未发布，请检查表单信息");
        }
        taskList.setFormName(form.getName());
        taskList.setFormScoreRule(form.getScoreRuleEnumNo());
        taskList.setFormScoreRuleName(form.getScoreRuleName());
        taskList.setFormTypeId(form.getTypeId());
        taskList.setFormTypeName(form.getTypeName());
        taskList.setFormId(form.getId());
        // 获取期望验收日期
        var node = acmsProjectService.getProjectNode(taskList.getProjectId(), projectInfo.getNodeTableName(), EnumProjectNode.PLAN_CHECK_DATE);
        if (StringUtils.hasLength(node.getNodeValue())) {
            taskList.setStartTime(DateUtils.toLocalDate(node.getNodeValue(), "yyyy-MM-dd").atTime(0, 0, 0));
        } else {
            taskList.setStartTime(LocalDateTime.now().plusDays(7));
        }
        var userRoleList = new ArrayList<UserRoleEntity>();
        // 获取项目成员
        var projectMemberList = projectService.createBuilder();
        // 获取交付支持中心竣工验收人员
        var user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.DELIVERY_ACCEPTANCE);
        var checkUserRole = userService.checkAndAddUserRole(user, EnumRole.DELIVERY_ACCEPTANCE);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        taskList.setCheckUserId(user.getId());
        taskList.setCheckUserName(user.getCnName());
        taskList.setCheckUserMobile(user.getPhone());
        taskList.setCheckUserNickName(user.getCnName());
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.DELIVERY_ACCEPTANCE.getCode(), EnumRole.DELIVERY_ACCEPTANCE.getName(), currentUser);

        // 获取交付支持中心竣工资料/整改复核人员
        user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.DELIVERY_REVIEW);
        checkUserRole = userService.checkAndAddUserRole(user, EnumRole.DELIVERY_REVIEW);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.DELIVERY_REVIEW.getCode(), EnumRole.DELIVERY_REVIEW.getName(), currentUser);

        // 获取开发战区营建项目经理
        user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.DEVELOPMENT_MANAGER);
        taskList.setPmUserId(user.getId());
        taskList.setPmUserName(user.getCnName());
        taskList.setPmUserNickName(user.getCnName());
        checkUserRole = userService.checkAndAddUserRole(user, EnumRole.DEVELOPMENT_MANAGER);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.DEVELOPMENT_MANAGER.getCode(), EnumRole.DEVELOPMENT_MANAGER.getName(), currentUser);

        // 获取经营战区现长
        user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.OPERATION_LEADER);
        checkUserRole = userService.checkAndAddUserRole(user, EnumRole.OPERATION_LEADER);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.OPERATION_LEADER.getCode(), EnumRole.OPERATION_LEADER.getName(), currentUser);

        // 获取交付支持中心负责人
        user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.DELIVERY_LEADER);
        checkUserRole = userService.checkAndAddUserRole(user, EnumRole.DELIVERY_LEADER);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.DELIVERY_LEADER.getCode(), EnumRole.DELIVERY_LEADER.getName(), currentUser);

        // 获取交付支持中心工程负责人
        user = acmsProjectService.getProjectMember(taskList.getProjectId(), EnumRole.ENGINEERING_LEADER);
        checkUserRole = userService.checkAndAddUserRole(user, EnumRole.ENGINEERING_LEADER);
        if (!checkUserRole.getExists()) {
            userRoleList.add(checkUserRole.getUserRole());
        }
        projectMemberList.add(projectInfo.getProjectId(), user.getId(), user.getCnName(), user.getAccount(), user.getPhone(), user.getCnName(), checkUserRole.getUserRole().getRoleId(), EnumRole.ENGINEERING_LEADER.getCode(), EnumRole.ENGINEERING_LEADER.getName(), currentUser);

        Db.tx(() -> {
            taskListMapper.insert(taskList);
            // 给验收人员飞书发送任务
            var message = messageService.createTaskBuilder(true)
                    .title("竣工验收任务")
                    .content(String.format("您有一个项目%s-%s的竣工验收任务，请及时处理", taskList.getProjectNo(), taskList.getProjectName()))
                    .businessId(taskList.getId())
                    .businessName("竣工验收")
                    .moduleId(EnumModule.Check.getIndex())
                    .moduleName(EnumModule.Check.getName())
                    .url("/audit-mobile-app/audit")
                    .projectMember(projectMemberList.build().get(0))
                    .createUser(currentUser)
                    .build();
            messageService.addMessage(message);
            // 保存项目相关人员
            projectService.addMember(projectMemberList.build());
            if (!userRoleList.isEmpty()) {
                userService.addRole(userRoleList);
            }
            return true;
        });
    }

    /**
     * 获取符合条件的需要同步的任务列表
     */
    public List<TaskListEntity> getPreSynTaskList() {
        // 状态是待整改、整改中、待复核、验收通过且验收状态未同步
        // 状态是整改通过，且整改状态是未同步
        return taskListMapper.selectListByQuery(QueryWrapper.create()
                .and(QueryMethods.column(TaskListEntity::getStatus).in(Arrays.asList(EnumTaskStatus.PENDING_REFORM.getIndex(), EnumTaskStatus.IN_REFORM.getIndex(), EnumTaskStatus.Reviewing.getIndex(), EnumTaskStatus.ACCEPTED.getIndex()))
                        .and(QueryMethods.column(TaskListEntity::getToAcmsCheckStatus).eq(EnumSynStatus.UN_SYN.getIndex())))
                .or(QueryMethods.column(TaskListEntity::getStatus).eq(EnumTaskStatus.REFORM_APPROVED.getIndex())
                        .and(QueryMethods.column(TaskListEntity::getToAcmsReformStatus).eq(EnumSynStatus.UN_SYN.getIndex())))
        );
    }

    /**
     * 同步状态给ACMS
     *
     * @param task 任务
     */
    public void synStatusToAcms(TaskListEntity task) {
        var updateTask = new TaskListEntity();
        updateTask.setId(task.getId());
        updateTask.buildModifiedInfo(null);
        if (task.getStatus() == EnumTaskStatus.REFORM_APPROVED.getIndex()) {
            updateTask.setToAcmsReformStatus(EnumSynStatus.SYNCED.getIndex());
        } else {
            updateTask.setToAcmsCheckStatus(EnumSynStatus.SYNCED.getIndex());
        }
        Db.tx(() -> {
            taskListMapper.update(updateTask, true);
            if (task.getStatus() == EnumTaskStatus.REFORM_APPROVED.getIndex()) {
                acmsProjectService.updateReformStatus(task.getProjectId(), task.getReformTime());
            } else {
                acmsProjectService.updateCheckStatus(task.getProjectId(), task.getCheckTime());
            }
            return true;
        });
    }
}
