package com.atour.cms.audit.controller;

import com.atour.cms.audit.dto.PageInfo;
import com.atour.cms.audit.dto.PageQuery;
import com.atour.cms.audit.dto.Response;
import com.atour.cms.audit.dto.task.FileTaskQueryDto;
import com.atour.cms.audit.entity.task.FileTaskEntity;
import com.atour.cms.audit.service.task.FileTaskService;
import com.atour.cms.audit.util.RestUtils;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 验收任务控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/fileTask")
public class FileTaskController {
    @Resource
    private FileTaskService fileTaskService;

    /**
     * 文件列表-分页
     *
     * @param pageQuery 查询条件
     * @return 结果
     */
    @PostMapping("/page")
    public Mono<Response<PageInfo<FileTaskEntity>>> getTaskPage(@RequestBody PageQuery<FileTaskQueryDto> pageQuery) {
        return fileTaskService.getFileTaskPage(pageQuery);
    }

    @PostMapping("/export")
    public Mono<Response<String>> exportPpt(@RequestBody List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return RestUtils.fail("参数 taskId 不能为空");
        }
        Mono<Response<String>> responseMono = fileTaskService.addExportPptFileTask(taskIds);
        return responseMono;
    }

    @DeleteMapping("/del")
    public Mono<Response<Integer>> delTask(@RequestBody List<String> ids) {
        return RestUtils.success(fileTaskService.delFileTask(ids));
    }

    @GetMapping("/execPPTTask")
    public Mono<Response<String>> execPPTTask() {
        fileTaskService.beginExportPptFileTask();
        return RestUtils.success("导出PPT任务成功启动！");
    }

}
