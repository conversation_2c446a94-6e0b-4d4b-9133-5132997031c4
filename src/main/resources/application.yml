#端口
server:
  port: 20001

spring:
  application:
    name: audit-service
  webflux:
    base-path: /audit-service
  profiles:
    active: uat

#Nacos配置
nacos:
  enabled: true
  server-addr: **************:8848
  namespace: atour
  group: dev

#Eureka配置
eureka:
  client:
    enabled: true
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}