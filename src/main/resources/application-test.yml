#端口
server:
  port: 20001

spring:
  application:
    name: audit-service
  webflux:
    base-path: /audit-service

#Nacos配置
nacos:
  enabled: true
  server-addr: **************:8848
  namespace: atour-test
  group: test

#Eureka配置
eureka:
  client:
    enabled: true
    service-url:
      defaultZone: http://test-eureka.at-our.com:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90